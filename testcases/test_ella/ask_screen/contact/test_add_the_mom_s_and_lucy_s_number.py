import pytest
import allure
from testcases.test_ella.ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage


@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("联系人相关")
class TestAskScreenAddMomSLucySNumber(SimpleAskScreenTest):
    """
    Ask Screen测试类 - 联系人相关
    命令: add the mom's and lucy's number
    图片: add the mom's and lucy's number
    """

    @allure.title("测试add the mom's and lucy's number")
    @allure.description("测试Ask Screen功能: add the mom's and lucy's number")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_add_the_mom_s_and_lucy_s_number(self, ella_floating_page):
        """测试add the mom's and lucy's number命令"""
        command = "add the mom's and lucy's number"
        expected_keywords = ['following number is recognized', 'save it as a contact']

        # 数据准备
        with allure.step("准备测试数据"):
            from tools.file_pusher import file_pusher
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("add the mom's and lucy's number")
            assert push_result, f"推送图片失败: add the mom's and lucy's number"

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {command}"):
            success, response_texts, verification_result = self.simple_floating_command_test(
                ella_floating_page, command, expected_keywords, verify_response=True
            )
            # 关闭图库应用
            photos_page.stop_app()
        # 断言结果
        with allure.step("验证测试结果"):
            assert success, f"命令执行失败: {command}"
            assert response_texts, "未获取到响应文本"

            # 验证响应包含期望关键词（至少匹配一个）
            self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)
