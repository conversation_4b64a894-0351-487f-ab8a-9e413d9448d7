import pytest
import allure
from testcases.test_ella.ask_screen.base_ask_screen_test import SimpleAskScreenTest
from pages.apps.ai_gallery.photos_page import AiGalleryPhotosPage


@allure.epic("Ella浮窗测试")
@allure.feature("Ask Screen功能")
@allure.story("联系人相关")
class TestAskScreenAddAllNumbersLucy(SimpleAskScreenTest):
    """
    Ask Screen测试类 - 联系人相关
    命令: add all the numbers to lucy
    图片: add all the numbers to lucy
    """

    @allure.title("测试add all the numbers to lucy")
    @allure.description("测试Ask Screen功能: add all the numbers to lucy")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_add_all_the_numbers_to_lucy(self, ella_floating_page):
        """测试add all the numbers to lucy命令"""
        command = "add all the numbers to lucy"
        expected_keywords = ['following number is recognized', 'save it as a contact']

        # 数据准备
        with allure.step("准备测试数据"):
            from tools.file_pusher import file_pusher
            from pages.apps.file_manager.main_page import FileManagerMainPage
            # 推送测试图片到设备
            push_result = file_pusher.push_ask_screen_image("add all the numbers to lucy")
            assert push_result, f"推送图片失败: add all the numbers to lucy"

            # 刷新文件系统设备
            file_manager_page = FileManagerMainPage()
            result = file_manager_page.start_app()
            if result:
                file_manager_page.wait_for_page_load()
                # 切换到最近文件页面并执行刷新
                file_manager_page.switch_to_recents()
                file_manager_page.refresh_file_list()

            # 打开图库并选择图片
            photos_page = AiGalleryPhotosPage()
            result = photos_page.start_app()
            if result:
                photos_page.wait_for_page_load()
                photos_page.click_photo()

        # 执行命令并验证
        with allure.step(f"执行Ask Screen命令: {command}"):
            success, response_texts, verification_result = self.simple_floating_command_test(
                ella_floating_page, command, expected_keywords, verify_response=True
            )
            # 关闭图库应用
            photos_page.stop_app()
        # 断言结果
        with allure.step("验证测试结果"):
            assert success, f"命令执行失败: {command}"
            assert response_texts, "未获取到响应文本"

            # 验证响应包含期望关键词（至少匹配一个）
            self.assert_floating_response_contains(response_texts, expected_keywords, match_all=False)

