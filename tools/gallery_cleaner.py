"""
快速相册清理工具
专门用于快速清理AI Gallery中的图片，简洁高效
"""
import subprocess
import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from core.logger import log
except ImportError:
    # 如果无法导入logger，使用print作为备选
    class SimpleLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
    log = SimpleLogger()


class QuickGalleryCleaner:
    """快速相册清理工具类"""

    def __init__(self):
        """初始化快速相册清理工具"""
        pass

    def _run_adb_command(self, cmd: str, timeout: int = 5) -> subprocess.CompletedProcess:
        """执行ADB命令（快速版本）"""
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=timeout
            )
            return result
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令执行超时: {cmd}")
            return subprocess.CompletedProcess(cmd, 1, "", "命令执行超时")
        except Exception as e:
            log.error(f"执行ADB命令时出错: {e}")
            return subprocess.CompletedProcess(cmd, 1, "", str(e))

    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            result = self._run_adb_command("adb devices", timeout=3)
            if result.returncode == 0 and "device" in result.stdout:
                return True
            else:
                log.error("❌ 设备未连接或ADB不可用")
                return False
        except Exception as e:
            log.error(f"检查设备连接失败: {e}")
            return False

    def quick_clear_all(self) -> bool:
        """⚡ 快速清理所有AI Gallery目录"""
        try:
            log.info("⚡ 快速清理AI Gallery...")
            
            if not self.check_device_connection():
                return False
            
            # 一条命令清理所有主要目录
            cmd = "adb shell rm -rf /sdcard/DCIM/Camera/* /sdcard/Pictures/* /sdcard/Download/*"
            result = self._run_adb_command(cmd, timeout=3)
            
            if result.returncode == 0:
                # 快速刷新媒体库
                self._quick_refresh()
                log.info("⚡ 快速清理完成")
                return True
            else:
                log.warning("⚠️ 快速清理失败")
                return False
                
        except Exception as e:
            log.error(f"快速清理失败: {e}")
            return False

    def instant_clear(self) -> bool:
        """⚡ 瞬间清理（最快速度）"""
        try:
            log.info("⚡ 瞬间清理模式...")
            
            if not self.check_device_connection():
                return False
            
            # 最简单粗暴的清理命令
            cmd = "adb shell rm -rf /sdcard/DCIM/Camera/* /sdcard/Pictures/* /sdcard/Download/*"
            result = self._run_adb_command(cmd, timeout=2)
            
            if result.returncode == 0:
                log.info("⚡ 瞬间清理完成")
                return True
            else:
                return False
                
        except Exception as e:
            log.debug(f"瞬间清理异常: {e}")
            return False

    def one_second_clear(self) -> bool:
        """⏱️ 一秒清理（极速模式）"""
        try:
            log.info("⏱️ 一秒清理模式...")
            
            if not self.check_device_connection():
                return False
            
            # 最快速的清理命令
            cmd = "adb shell rm -rf /sdcard/DCIM/Camera/* /sdcard/Pictures/* /sdcard/Download/*"
            result = self._run_adb_command(cmd, timeout=1)
            
            if result.returncode == 0:
                log.info("⏱️ 一秒清理完成")
                return True
            else:
                return False
                
        except Exception as e:
            log.debug(f"一秒清理异常: {e}")
            return False

    def _quick_refresh(self) -> bool:
        """快速刷新媒体库"""
        try:
            cmd = "adb shell am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard/"
            self._run_adb_command(cmd, timeout=2)
            time.sleep(0.5)
            return True
        except Exception:
            return False


# 全局实例
quick_gallery_cleaner = QuickGalleryCleaner()


# 便捷函数 - 只保留最常用的
def quick_clear_ai_gallery() -> bool:
    """⚡ 快速清理AI Gallery（推荐使用）"""
    return quick_gallery_cleaner.quick_clear_all()


def instant_clear() -> bool:
    """⚡ 瞬间清理（最快速度）"""
    return quick_gallery_cleaner.instant_clear()


def one_second_clear() -> bool:
    """⏱️ 一秒清理（极速模式）"""
    return quick_gallery_cleaner.one_second_clear()


def super_quick_clear() -> bool:
    """🚀 超快速清理（别名，指向快速清理）"""
    return quick_gallery_cleaner.quick_clear_all()


# 兼容性函数（保持向后兼容）
def clear_camera_folder() -> bool:
    """清理相机文件夹（使用快速清理）"""
    return quick_clear_ai_gallery()


def clear_all_galleries() -> bool:
    """清理所有相册（使用快速清理）"""
    return quick_clear_ai_gallery()

cleaner = QuickGalleryCleaner()

if __name__ == "__main__":
    # 测试快速清理功能
    print("🎯 测试快速相册清理功能")
    
    cleaner = QuickGalleryCleaner()
    
    if cleaner.check_device_connection():
        print("✅ 设备连接正常")
        
        # 测试快速清理
        print("\n⚡ 执行快速清理...")
        import time
        start_time = time.time()
        success = cleaner.quick_clear_all()
        end_time = time.time()
        
        print(f"清理结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
    else:
        print("❌ 设备未连接，请确保设备已连接并启用USB调试")
