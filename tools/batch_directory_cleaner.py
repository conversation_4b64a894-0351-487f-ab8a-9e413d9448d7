#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量目录清理工具
用于批量清理Android设备上的指定目录文件

作者: AI Assistant
创建时间: 2025-08-27
"""

import sys
import json
from pathlib import Path
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tools.file_pusher import FilePusher
from core.logger import log


class BatchDirectoryCleaner:
    """批量目录清理器"""
    
    def __init__(self):
        """初始化清理器"""
        self.pusher = FilePusher()
        
        # 预定义的目录配置
        self.preset_configs = {
            "common": [
                {"path": "/sdcard/DCIM/Camera", "description": "相机拍照目录"},
                {"path": "/sdcard/Pictures/Screenshot", "description": "截图目录"},
                {"path": "/sdcard/Movies/ScreenRecord", "description": "录屏目录"},
                {"path": "/sdcard/Download", "description": "下载目录"}
            ],
            "media": [
                {"path": "/sdcard/DCIM/Camera", "description": "相机拍照目录"},
                {"path": "/sdcard/Pictures", "description": "图片目录"},
                {"path": "/sdcard/Movies", "description": "视频目录"},
                {"path": "/sdcard/Music", "description": "音乐目录"}
            ],
            "cache": [
                {"path": "/sdcard/Android/data", "description": "应用数据目录"},
                {"path": "/data/local/tmp", "description": "临时文件目录"}
            ],
            "test": [
                {"path": "/sdcard/Download", "description": "下载目录"},
                {"path": "/sdcard/DCIM/Camera", "description": "相机目录"}
            ]
        }
    
    def clean_preset_directories(self, preset_name: str = "common", file_pattern: str = "*") -> Dict:
        """
        清理预设的目录配置
        
        Args:
            preset_name: 预设配置名称 (common, media, cache, test)
            file_pattern: 文件匹配模式
            
        Returns:
            Dict: 清理结果
        """
        try:
            if preset_name not in self.preset_configs:
                available_presets = list(self.preset_configs.keys())
                log.error(f"未知的预设配置: {preset_name}, 可用配置: {available_presets}")
                return {"success": False, "error": f"未知的预设配置: {preset_name}"}
            
            directories = self.preset_configs[preset_name]
            log.info(f"使用预设配置 '{preset_name}' 清理 {len(directories)} 个目录")
            
            return self.pusher.batch_clean_directories(directories, file_pattern)
            
        except Exception as e:
            log.error(f"清理预设目录时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def clean_custom_directories(self, directories: List[Dict], file_pattern: str = "*") -> Dict:
        """
        清理自定义目录列表
        
        Args:
            directories: 自定义目录配置列表
            file_pattern: 文件匹配模式
            
        Returns:
            Dict: 清理结果
        """
        try:
            log.info(f"清理自定义目录列表，共 {len(directories)} 个目录")
            return self.pusher.batch_clean_directories(directories, file_pattern)
            
        except Exception as e:
            log.error(f"清理自定义目录时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def clean_from_config_file(self, config_file: str, file_pattern: str = "*") -> Dict:
        """
        从配置文件读取目录列表并清理
        
        Args:
            config_file: 配置文件路径 (JSON格式)
            file_pattern: 文件匹配模式
            
        Returns:
            Dict: 清理结果
        """
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                log.error(f"配置文件不存在: {config_file}")
                return {"success": False, "error": f"配置文件不存在: {config_file}"}
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if "directories" not in config_data:
                log.error("配置文件中缺少 'directories' 字段")
                return {"success": False, "error": "配置文件格式错误"}
            
            directories = config_data["directories"]
            log.info(f"从配置文件加载 {len(directories)} 个目录")
            
            return self.pusher.batch_clean_directories(directories, file_pattern)
            
        except json.JSONDecodeError as e:
            log.error(f"配置文件JSON格式错误: {e}")
            return {"success": False, "error": f"配置文件JSON格式错误: {e}"}
        except Exception as e:
            log.error(f"从配置文件清理时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def check_device_status(self) -> bool:
        """检查设备连接状态"""
        return self.pusher.check_device_connection()
    
    def list_preset_configs(self) -> Dict[str, List[Dict]]:
        """列出所有预设配置"""
        return self.preset_configs.copy()
    
    def generate_sample_config(self, output_file: str = "clean_config_sample.json") -> bool:
        """
        生成示例配置文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            bool: 是否生成成功
        """
        try:
            sample_config = {
                "description": "批量目录清理配置文件示例",
                "directories": [
                    {
                        "path": "/sdcard/Download",
                        "description": "下载目录"
                    },
                    {
                        "path": "/sdcard/DCIM/Camera", 
                        "description": "相机拍照目录"
                    },
                    {
                        "path": "/sdcard/Pictures/Screenshot",
                        "description": "截图目录"
                    }
                ]
            }
            
            output_path = Path(output_file)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, ensure_ascii=False, indent=2)
            
            log.info(f"示例配置文件已生成: {output_path}")
            return True
            
        except Exception as e:
            log.error(f"生成示例配置文件失败: {e}")
            return False


def print_usage():
    """打印使用说明"""
    print("""
=== 批量目录清理工具使用说明 ===

基本用法:
  python batch_directory_cleaner.py [选项]

选项:
  --preset <name>        使用预设配置 (common, media, cache, test)
  --config <file>        使用配置文件
  --pattern <pattern>    文件匹配模式 (默认: *)
  --list-presets         列出所有预设配置
  --generate-config      生成示例配置文件
  --help                 显示此帮助信息

示例:
  # 清理常用目录
  python batch_directory_cleaner.py --preset common
  
  # 清理特定文件类型
  python batch_directory_cleaner.py --preset common --pattern "*.jpg"
  
  # 使用配置文件
  python batch_directory_cleaner.py --config my_config.json
  
  # 列出预设配置
  python batch_directory_cleaner.py --list-presets
  
  # 生成示例配置文件
  python batch_directory_cleaner.py --generate-config
""")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="批量目录清理工具")
    parser.add_argument("--preset", help="使用预设配置 (common, media, cache, test)")
    parser.add_argument("--config", help="使用配置文件")
    parser.add_argument("--pattern", default="*", help="文件匹配模式 (默认: *)")
    parser.add_argument("--list-presets", action="store_true", help="列出所有预设配置")
    parser.add_argument("--generate-config", nargs='?', const="clean_config_sample.json", help="生成示例配置文件")
    
    args = parser.parse_args()
    
    # 创建清理器
    cleaner = BatchDirectoryCleaner()
    
    # 处理命令行参数
    if args.list_presets:
        print("=== 可用的预设配置 ===")
        presets = cleaner.list_preset_configs()
        for name, dirs in presets.items():
            print(f"\n{name}:")
            for dir_config in dirs:
                print(f"  - {dir_config['description']}: {dir_config['path']}")
        return
    
    if args.generate_config:
        output_file = args.generate_config if args.generate_config != True else "clean_config_sample.json"
        if cleaner.generate_sample_config(output_file):
            print(f"✅ 示例配置文件已生成: {output_file}")
        else:
            print("❌ 生成示例配置文件失败")
        return
    
    # 检查设备连接
    if not cleaner.check_device_status():
        print("❌ 设备未连接或USB调试未启用")
        return
    
    # 执行清理
    result = None
    
    if args.preset:
        print(f"使用预设配置 '{args.preset}' 清理目录...")
        result = cleaner.clean_preset_directories(args.preset, args.pattern)
    elif args.config:
        print(f"使用配置文件 '{args.config}' 清理目录...")
        result = cleaner.clean_from_config_file(args.config, args.pattern)
    else:
        print("使用默认配置清理常用目录...")
        result = cleaner.clean_preset_directories("common", args.pattern)
    
    # 输出结果
    if result and result.get("success"):
        print(f"\n✅ 清理完成: 成功 {result['success_count']}/{result['total']} 个目录")
        if result.get("failed_count", 0) > 0:
            print(f"❌ 失败的目录: {', '.join(result.get('failed_dirs', []))}")
    else:
        error_msg = result.get("error", "未知错误") if result else "清理失败"
        print(f"\n❌ 清理失败: {error_msg}")


if __name__ == "__main__":
    main()
