#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure报告转Excel工具使用示例
演示如何在Python代码中使用AllureToExcelConverter
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加tools目录到Python路径
tools_dir = Path(__file__).parent.parent
sys.path.insert(0, str(tools_dir))

try:
    from allure_report_analysis.allure_to_excel import AllureToExcelConverter
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 allure_to_excel.py 文件存在于tools/allure_report_analysis目录下")
    sys.exit(1)


def example_basic_usage():
    """基础使用示例"""
    print("=" * 60)
    print("📊 基础使用示例")
    print("=" * 60)
    
    # 创建转换器
    converter = AllureToExcelConverter("reports")
    
    try:
        # 执行转换
        output_file = converter.convert_to_excel()
        print(f"✅ 转换成功！文件保存到: {output_file}")
        return output_file
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None


def example_custom_reports_dir():
    """自定义报告目录示例"""
    print("\n" + "=" * 60)
    print("📁 自定义报告目录示例")
    print("=" * 60)
    
    # 使用自定义报告目录
    custom_reports_dir = "custom_reports"
    
    # 检查目录是否存在
    if not os.path.exists(custom_reports_dir):
        print(f"⚠️  自定义报告目录不存在: {custom_reports_dir}")
        print("使用默认的reports目录")
        custom_reports_dir = "reports"
    
    converter = AllureToExcelConverter(custom_reports_dir)
    
    try:
        output_file = converter.convert_to_excel()
        print(f"✅ 转换成功！文件保存到: {output_file}")
        return output_file
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None


def example_analyze_results():
    """分析转换结果示例"""
    print("\n" + "=" * 60)
    print("📈 分析转换结果示例")
    print("=" * 60)
    
    converter = AllureToExcelConverter("reports")
    
    # 先提取数据（不生成Excel）
    test_results = converter._extract_test_results()
    container_info = converter._extract_container_info()
    
    print(f"📊 数据统计:")
    print(f"   - 测试用例总数: {len(test_results)}")
    print(f"   - 容器总数: {len(container_info)}")
    
    if test_results:
        # 统计各种状态
        status_counts = {}
        for result in test_results:
            status = result.get('测试状态', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"\n📋 测试状态分布:")
        total = len(test_results)
        for status, count in status_counts.items():
            percentage = (count / total * 100) if total > 0 else 0
            print(f"   - {status}: {count} ({percentage:.1f}%)")
        
        # 分析执行时间
        durations = []
        for result in test_results:
            duration_str = result.get('执行时长(ms)', '')
            if duration_str and duration_str.isdigit():
                durations.append(int(duration_str))
        
        if durations:
            avg_duration = sum(durations) / len(durations)
            max_duration = max(durations)
            min_duration = min(durations)
            
            print(f"\n⏱️  执行时间分析:")
            print(f"   - 平均执行时间: {avg_duration:.0f}ms")
            print(f"   - 最长执行时间: {max_duration}ms")
            print(f"   - 最短执行时间: {min_duration}ms")
    
    # 生成Excel文件
    try:
        output_file = converter.convert_to_excel()
        print(f"\n✅ Excel文件已生成: {output_file}")
        return output_file
    except Exception as e:
        print(f"\n❌ 生成Excel文件失败: {e}")
        return None


def example_batch_processing():
    """批量处理示例"""
    print("\n" + "=" * 60)
    print("🔄 批量处理示例")
    print("=" * 60)
    
    # 模拟处理多个报告目录
    report_dirs = ["reports"]  # 可以添加更多目录
    
    results = []
    for report_dir in report_dirs:
        print(f"\n📁 处理目录: {report_dir}")
        
        if not os.path.exists(report_dir):
            print(f"⚠️  目录不存在，跳过: {report_dir}")
            continue
        
        converter = AllureToExcelConverter(report_dir)
        
        try:
            output_file = converter.convert_to_excel()
            results.append(output_file)
            print(f"✅ 成功: {output_file}")
        except Exception as e:
            print(f"❌ 失败: {e}")
    
    print(f"\n📊 批量处理完成，成功生成 {len(results)} 个Excel文件:")
    for result in results:
        print(f"   - {result}")
    
    return results


def example_error_handling():
    """错误处理示例"""
    print("\n" + "=" * 60)
    print("🛠️  错误处理示例")
    print("=" * 60)
    
    # 测试不存在的目录
    print("测试不存在的目录...")
    converter = AllureToExcelConverter("non_existent_directory")
    
    try:
        output_file = converter.convert_to_excel()
        print(f"意外成功: {output_file}")
    except Exception as e:
        print(f"✅ 正确捕获错误: {e}")
    
    # 测试空目录
    print("\n测试空的allure-results目录...")
    empty_dir = "empty_reports"
    os.makedirs(empty_dir, exist_ok=True)
    os.makedirs(f"{empty_dir}/allure-results", exist_ok=True)
    
    converter = AllureToExcelConverter(empty_dir)
    
    try:
        output_file = converter.convert_to_excel()
        print(f"✅ 处理空目录成功: {output_file}")
        
        # 清理临时目录
        import shutil
        shutil.rmtree(empty_dir)
        
    except Exception as e:
        print(f"处理空目录时出错: {e}")


def main():
    """主函数"""
    print("🚀 Allure报告转Excel工具使用示例")
    print(f"⏰ 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查reports目录是否存在
    if not os.path.exists("reports"):
        print("\n❌ reports目录不存在")
        print("请确保已经运行过Allure测试并生成了报告")
        return 1
    
    # 运行各种示例
    try:
        # 基础使用
        example_basic_usage()
        
        # 自定义目录
        example_custom_reports_dir()
        
        # 分析结果
        example_analyze_results()
        
        # 批量处理
        example_batch_processing()
        
        # 错误处理
        example_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成！")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"\n\n❌ 运行示例时出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
