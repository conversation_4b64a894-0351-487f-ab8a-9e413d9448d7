#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动修复测试脚本断言值工具
根据Excel失败分析报告自动修复测试脚本中的expected_text断言值
"""

import os
import sys
import re
import pandas as pd
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class AssertionFixer:
    """断言修复器"""
    
    def __init__(self, excel_path):
        """
        初始化断言修复器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.project_root = project_root
        self.fixed_count = 0
        self.error_count = 0
        self.log_messages = []
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        self.log_messages.append(log_msg)
    
    def read_excel_data(self):
        """读取Excel数据"""
        try:
            self.log(f"正在读取Excel文件: {self.excel_path}")
            df = pd.read_excel(self.excel_path)
            self.log(f"成功读取Excel文件，共{len(df)}行数据")
            return df
        except Exception as e:
            self.log(f"读取Excel文件失败: {str(e)}")
            return None
    
    def filter_failure_data(self, df):
        """过滤需要修复的失败数据"""
        # 过滤指令支持变更的数据
        support_change_data = df[
            (df['失败原因'] == '指令支持变更：支持->不支持') |
            (df['失败原因'] == '指令支持变更：不支持->支持')
        ].copy()
        
        self.log(f"找到{len(support_change_data)}条需要修复的数据")
        return support_change_data
    
    def get_script_path(self, parent_suite):
        """根据标签_parentSuite获取脚本路径"""
        if not parent_suite:
            return None
            
        # 将点号分隔的路径转换为文件系统路径
        # 例如: testcases.test_ella.unsupported_commands -> testcases/test_ella/unsupported_commands
        path_parts = parent_suite.split('.')
        script_dir = self.project_root / Path(*path_parts)
        
        return script_dir
    
    def determine_new_expected_text(self, failure_reason, actual_result):
        """根据失败原因和实际结果确定新的expected_text值"""
        if failure_reason == '指令支持变更：支持->不支持':
            # 支持变更为不支持，应该修改为 ['sorry']
            return "['sorry']"
        elif failure_reason == '指令支持变更：不支持->支持':
            # 不支持变更为支持，根据实际结果确定
            if 'Done' in actual_result:
                return "['Done']"
            elif 'Multiple settings options found' in actual_result:
                return "['Multiple settings options found']"
            else:
                # 默认使用Done
                return "['Done']"
        return None
    
    def find_specific_test_file(self, directory, test_name):
        """根据测试名称查找特定的测试文件"""
        if not directory.exists() or not directory.is_dir():
            return None

        # 清理测试名称，移除可能的前缀
        clean_test_name = test_name.replace("test_", "")

        # 方法1: 直接匹配文件名
        expected_filename = f"test_{clean_test_name}.py"
        test_file = directory / expected_filename

        if test_file.exists():
            return test_file

        # 方法2: 在目录中搜索包含测试名称的文件
        for file in directory.glob("test_*.py"):
            if clean_test_name in file.stem or test_name in file.stem:
                return file

        # 方法3: 模糊匹配（处理文件名中的特殊字符）
        for file in directory.glob("test_*.py"):
            # 将文件名和测试名称都转换为小写进行比较
            file_stem_lower = file.stem.lower()
            test_name_lower = clean_test_name.lower()
            if test_name_lower in file_stem_lower:
                return file

        return None
    
    def fix_expected_text_in_file(self, file_path, new_expected_text, test_name):
        """在文件中修复expected_text"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否已经被修复过
            if "# tools 修复" in content:
                return False, "文件已经被修复过，跳过"

            # 验证文件是否包含对应的测试函数
            clean_test_name = test_name.replace("test_", "")
            test_function_pattern = rf'def\s+test_{re.escape(clean_test_name)}\s*\('

            if not re.search(test_function_pattern, content):
                return False, f"文件中未找到测试函数 test_{clean_test_name}"

            # 查找expected_text的模式
            # 匹配 expected_text = ['xxx'] 或 expected_text = ["xxx"] 等格式
            pattern = r'(expected_text\s*=\s*)\[([^\]]+)\]'

            matches = re.findall(pattern, content)
            if not matches:
                return False, "未找到expected_text定义"

            # 获取当前时间用于注释
            current_time = datetime.now().strftime("%Y.%m.%d_%H:%M:%S")

            # 替换expected_text值并添加注释
            def replace_func(match):
                prefix = match.group(1)
                return f"{prefix}{new_expected_text}  # tools 修复 {current_time}"

            new_content = re.sub(pattern, replace_func, content)

            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return True, f"成功修复expected_text为{new_expected_text}"

        except Exception as e:
            return False, f"修复文件失败: {str(e)}"
    
    def process_failure_record(self, record):
        """处理单条失败记录"""
        parent_suite = record.get('标签_parentSuite', '')
        failure_reason = record.get('失败原因', '')
        actual_result = record.get('实际结果', '')
        test_name = record.get('标签_suite', '')

        self.log(f"处理测试: {test_name}")
        self.log(f"  父套件: {parent_suite}")
        self.log(f"  失败原因: {failure_reason}")

        # 获取脚本目录
        script_dir = self.get_script_path(parent_suite)
        if not script_dir:
            self.log(f"  错误: 无法解析脚本路径")
            self.error_count += 1
            return

        # 确定新的expected_text值
        new_expected_text = self.determine_new_expected_text(failure_reason, actual_result)
        if not new_expected_text:
            self.log(f"  错误: 无法确定新的expected_text值")
            self.error_count += 1
            return

        self.log(f"  新expected_text: {new_expected_text}")

        # 查找特定的测试文件（而不是所有文件）
        test_file = self.find_specific_test_file(script_dir, test_name)
        if not test_file:
            self.log(f"  错误: 在目录 {script_dir} 中未找到测试文件 {test_name}")
            self.error_count += 1
            return

        self.log(f"  找到测试文件: {test_file.relative_to(self.project_root)}")

        # 修复特定文件中的expected_text
        success, message = self.fix_expected_text_in_file(test_file, new_expected_text, test_name)
        if success:
            self.fixed_count += 1
            self.log(f"  ✓ 修复文件: {test_file.relative_to(self.project_root)}")
        else:
            self.error_count += 1
            self.log(f"  ✗ 修复失败 {test_file.relative_to(self.project_root)}: {message}")
    
    def run(self):
        """运行修复流程"""
        self.log("开始自动修复断言值...")
        
        # 读取Excel数据
        df = self.read_excel_data()
        if df is None:
            return False
        
        # 过滤需要修复的数据
        failure_data = self.filter_failure_data(df)
        if len(failure_data) == 0:
            self.log("没有找到需要修复的数据")
            return True

        # 处理每条失败记录
        for index, record in failure_data.iterrows():
            self.process_failure_record(record)
        
        # 输出总结
        self.log(f"\n修复完成!")
        self.log(f"成功修复文件数: {self.fixed_count}")
        self.log(f"失败记录数: {self.error_count}")
        
        return True
    
    def save_log(self):
        """保存日志到文件"""
        log_dir = self.project_root / "temp"
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"assertion_fix_log_{timestamp}.txt"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(self.log_messages))
        
        self.log(f"日志已保存到: {log_file}")


def main():
    """主函数"""

    excel_dir = os.path.join(os.path.dirname(__file__),'failure_analysis')
    file_name='failure_analysis_allure_report_25-09-01_17-08-18.xlsx'
    # # Excel文件路径
    excel_file = os.path.join(excel_dir,file_name)

    if not os.path.exists(excel_file):
        print(f"错误: Excel文件不存在: {excel_file}")
        return False

    # 创建修复器并运行
    fixer = AssertionFixer(excel_file)
    success = fixer.run()
    fixer.save_log()

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)


