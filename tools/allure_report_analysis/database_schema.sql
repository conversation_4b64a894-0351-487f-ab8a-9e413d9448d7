-- Ella Allure测试报告数据库表结构
-- 创建时间: 2025-09-04

-- 测试执行记录表
CREATE TABLE IF NOT EXISTS `test_executions` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行批次ID（UUID）',
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
    `device_name` VARCHAR(200) DEFAULT NULL COMMENT '设备名称',
    `device_brand` VARCHAR(100) DEFAULT NULL COMMENT '设备品牌',
    `device_model` VARCHAR(100) DEFAULT NULL COMMENT '设备型号',
    `android_version` VARCHAR(50) DEFAULT NULL COMMENT 'Android版本',
    `platform_version` VARCHAR(50) DEFAULT NULL COMMENT '平台版本',
    `execution_date` DATE NOT NULL COMMENT '执行日期',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `duration_ms` BIGINT DEFAULT NULL COMMENT '执行时长（毫秒）',
    `total_cases` INT DEFAULT 0 COMMENT '总用例数',
    `passed_count` INT DEFAULT 0 COMMENT '通过数',
    `failed_count` INT DEFAULT 0 COMMENT '失败数',
    `broken_count` INT DEFAULT 0 COMMENT '中断数',
    `skipped_count` INT DEFAULT 0 COMMENT '跳过数',
    `unknown_count` INT DEFAULT 0 COMMENT '未知数',
    `success_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '成功率（%）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_execution_date` (`execution_date`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试执行记录表';

-- 测试用例结果表
CREATE TABLE IF NOT EXISTS `test_case_results` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行批次ID',
    `uuid` VARCHAR(64) NOT NULL COMMENT '用例UUID',
    `test_case_name` VARCHAR(500) NOT NULL COMMENT '测试用例名称',
    `test_status` VARCHAR(20) NOT NULL COMMENT '测试状态（passed/failed/broken/skipped/unknown）',
    `description` TEXT DEFAULT NULL COMMENT '用例描述',
    `full_name` VARCHAR(1000) DEFAULT NULL COMMENT '完整名称',
    `history_id` VARCHAR(100) DEFAULT NULL COMMENT '历史ID',
    `test_case_id` VARCHAR(100) DEFAULT NULL COMMENT '测试用例ID',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `duration_ms` BIGINT DEFAULT NULL COMMENT '执行时长（毫秒）',
    `failure_message` TEXT DEFAULT NULL COMMENT '失败消息',
    `failure_trace` LONGTEXT DEFAULT NULL COMMENT '失败堆栈',
    `failure_reason` TEXT DEFAULT NULL COMMENT '失败原因简述',
    `steps_count` INT DEFAULT 0 COMMENT '步骤数量',
    `steps_details` LONGTEXT DEFAULT NULL COMMENT '步骤详情',
    `attachments_count` INT DEFAULT 0 COMMENT '附件数量',
    `attachments_details` TEXT DEFAULT NULL COMMENT '附件详情',
    `parameters_count` INT DEFAULT 0 COMMENT '参数数量',
    `parameters_details` TEXT DEFAULT NULL COMMENT '参数详情',
    `command_type` ENUM('text', 'voice') DEFAULT 'text' COMMENT '指令类型（文本/语音）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_test_status` (`test_status`),
    INDEX `idx_test_case_name` (`test_case_name`(255)),
    INDEX `idx_command_type` (`command_type`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`execution_id`) REFERENCES `test_executions`(`execution_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例结果表';

-- 测试用例标签表
CREATE TABLE IF NOT EXISTS `test_case_labels` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行批次ID',
    `test_case_uuid` VARCHAR(64) NOT NULL COMMENT '测试用例UUID',
    `label_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
    `label_value` VARCHAR(500) DEFAULT NULL COMMENT '标签值',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_test_case_uuid` (`test_case_uuid`),
    INDEX `idx_label_name` (`label_name`),
    INDEX `idx_label_value` (`label_value`(255)),
    FOREIGN KEY (`execution_id`) REFERENCES `test_executions`(`execution_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试用例标签表';

-- 容器信息表
CREATE TABLE IF NOT EXISTS `test_containers` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行批次ID',
    `uuid` VARCHAR(64) NOT NULL COMMENT '容器UUID',
    `children` TEXT DEFAULT NULL COMMENT '子项列表',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `duration_ms` BIGINT DEFAULT NULL COMMENT '执行时长（毫秒）',
    `before_hooks` TEXT DEFAULT NULL COMMENT '前置操作',
    `after_hooks` TEXT DEFAULT NULL COMMENT '后置操作',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_uuid` (`uuid`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`execution_id`) REFERENCES `test_executions`(`execution_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='测试容器信息表';
