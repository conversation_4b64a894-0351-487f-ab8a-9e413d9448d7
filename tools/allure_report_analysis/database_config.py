#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置和连接管理模块
"""

import pymysql
import logging
from dataclasses import dataclass
from typing import Optional, Dict, Any
from contextlib import contextmanager


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = "root123456"
    database: str = "ella_allure_report"
    charset: str = "utf8mb4"


class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self, config: DatabaseConfig = None):
        """
        初始化数据库管理器
        
        Args:
            config: 数据库配置，如果为None则使用默认配置
        """
        self.config = config or DatabaseConfig()
        self.logger = logging.getLogger(__name__)
        
    def get_connection(self) -> pymysql.Connection:
        """
        获取数据库连接
        
        Returns:
            pymysql.Connection: 数据库连接对象
            
        Raises:
            Exception: 连接失败时抛出异常
        """
        try:
            connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                database=self.config.database,
                charset=self.config.charset,
                autocommit=False,
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_cursor(self):
        """
        获取数据库游标的上下文管理器
        
        Yields:
            pymysql.cursors.DictCursor: 数据库游标
        """
        connection = None
        cursor = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            yield cursor
            connection.commit()
        except Exception as e:
            if connection:
                connection.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            with self.get_cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def create_database_if_not_exists(self) -> bool:
        """
        创建数据库（如果不存在）
        
        Returns:
            bool: 创建成功返回True，失败返回False
        """
        try:
            # 连接到MySQL服务器（不指定数据库）
            connection = pymysql.connect(
                host=self.config.host,
                port=self.config.port,
                user=self.config.username,
                password=self.config.password,
                charset=self.config.charset,
                autocommit=True
            )
            
            with connection.cursor() as cursor:
                # 创建数据库
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{self.config.database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                self.logger.info(f"数据库 {self.config.database} 创建成功或已存在")
                
            connection.close()
            return True
            
        except Exception as e:
            self.logger.error(f"创建数据库失败: {e}")
            return False
    
    def execute_sql_file(self, sql_file_path: str) -> bool:
        """
        执行SQL文件
        
        Args:
            sql_file_path: SQL文件路径
            
        Returns:
            bool: 执行成功返回True，失败返回False
        """
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（以分号分隔）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            with self.get_cursor() as cursor:
                for sql in sql_statements:
                    if sql:
                        cursor.execute(sql)
                        
            self.logger.info(f"SQL文件 {sql_file_path} 执行成功")
            return True
            
        except Exception as e:
            self.logger.error(f"执行SQL文件失败: {e}")
            return False
