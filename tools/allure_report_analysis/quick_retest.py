#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败用例重测工具 - 快速启动脚本
提供简化的命令行接口，快速执行失败用例重测

作者: AI Assistant
创建时间: 2025-09-03
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.allure_report_analysis.failure_retest_tool import FailureRetestTool
from core.logger import log


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="失败用例重测工具 - 快速启动",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用最新的失败分析文件生成重测脚本
  python tools/allure_report_analysis/quick_retest.py

  # 指定特定的Excel文件
  python tools/allure_report_analysis/quick_retest.py -f failure_analysis.xlsx

  # 生成脚本后立即执行重测
  python tools/allure_report_analysis/quick_retest.py --run

  # 运行演示
  python tools/allure_report_analysis/quick_retest.py --demo

说明:
  本工具会自动读取失败分析Excel文件，过滤出指令支持变更相关的失败用例，
  生成pytest重测脚本，并可选择立即执行重测。

  过滤条件:
  - 失败原因 = '指令支持变更：支持->不支持'
  - 失败原因 = '指令支持变更：不支持->支持'

  路径推断规则:
  - 标签_parentSuite: testcases.test_ella.system_coupling -> testcases/test_ella/system_coupling
  - 标签_suite: test_turn_off_adaptive_brightness -> test_turn_off_adaptive_brightness.py
  - 完整路径: testcases/test_ella/system_coupling/test_turn_off_adaptive_brightness.py
        """
    )
    
    parser.add_argument(
        "-f", "--file",
        dest="excel_path",
        help="指定失败分析Excel文件路径，不指定则自动查找最新的"
    )
    
    parser.add_argument(
        "--run",
        action="store_true",
        help="生成重测脚本后立即执行"
    )
    
    parser.add_argument(
        "--demo",
        action="store_true",
        help="运行演示模式，使用模拟数据展示功能"
    )
    
    parser.add_argument(
        "--list-files",
        action="store_true",
        help="列出可用的失败分析Excel文件"
    )
    
    args = parser.parse_args()
    
    try:
        # 如果是演示模式
        if args.demo:
            log.info("=== 演示模式 ===")
            demo_script = project_root / "temp" / "demo_failure_retest_tool.py"
            if demo_script.exists():
                import subprocess
                result = subprocess.run([sys.executable, str(demo_script)], cwd=project_root)
                return result.returncode
            else:
                log.error(f"演示脚本不存在: {demo_script}")
                return 1
        
        # 如果是列出文件模式
        if args.list_files:
            log.info("=== 可用的失败分析Excel文件 ===")
            failure_analysis_dir = project_root / "tools" / "allure_report_analysis" / "failure_analysis"

            if failure_analysis_dir.exists():
                excel_files = list(failure_analysis_dir.glob("failure_analysis_*.xlsx"))
                if excel_files:
                    # 创建临时工具实例用于时间戳提取
                    temp_tool = FailureRetestTool()

                    # 按文件名中的时间戳排序
                    excel_files.sort(key=temp_tool._extract_timestamp_from_filename, reverse=True)

                    log.info(f"找到 {len(excel_files)} 个失败分析文件（按文件名时间戳排序）:")
                    for i, file_path in enumerate(excel_files, 1):
                        # 提取文件名中的时间戳
                        timestamp = temp_tool._extract_timestamp_from_filename(file_path)
                        timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        log.info(f"  {i}. {file_path.name} (时间戳: {timestamp_str})")

                    log.info(f"\n最新文件（基于文件名时间戳）: {excel_files[0].name}")
                else:
                    log.warning("没有找到失败分析Excel文件")
            else:
                log.error(f"失败分析目录不存在: {failure_analysis_dir}")

            return 0
        
        # 正常模式
        log.info("=== 失败用例重测工具 ===")
        
        # 创建重测工具
        tool = FailureRetestTool(args.excel_path)
        
        # 加载和过滤Excel数据
        log.info("正在加载和过滤Excel数据...")
        if not tool.load_and_filter_excel():
            log.error("❌ 加载Excel文件失败")
            return 1
        
        # 检查是否有符合条件的用例
        if not tool.filtered_cases:
            log.warning("⚠️ 没有找到符合条件的失败用例")
            log.info("提示: 确保Excel文件中的'失败原因'字段包含以下值之一:")
            for reason in tool.target_failure_reasons:
                log.info(f"  - {reason}")
            return 0
        
        # 生成重测脚本
        log.info("正在生成重测脚本...")
        script_path = tool.create_retest_script()
        
        if not script_path:
            log.error("❌ 生成重测脚本失败")
            return 1
        
        log.info(f"✅ 重测脚本生成成功: {script_path}")
        
        # 如果指定立即执行
        if args.run:
            log.info("开始执行重测...")
            import subprocess
            result = subprocess.run([sys.executable, script_path], cwd=project_root)
            
            if result.returncode == 0:
                log.info("✅ 重测执行完成，所有测试通过")
            else:
                log.warning(f"⚠️ 重测执行完成，存在失败用例，退出码: {result.returncode}")
            
            return result.returncode
        else:
            log.info("重测脚本已生成，可以手动执行:")
            log.info(f"  python {script_path}")
            return 0
            
    except KeyboardInterrupt:
        log.info("用户中断操作")
        return 1
    except Exception as e:
        log.error(f"❌ 执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
