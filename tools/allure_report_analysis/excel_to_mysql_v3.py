#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel报告转MySQL数据库工具 V3 (优化版)
将allure_report_excel目录下的Excel文件导入到MySQL数据库中
仅使用核心测试结果表
"""

import os
import uuid
import yaml
import sys
import pandas as pd
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from tools.allure_report_analysis.database_config import DatabaseManager, DatabaseConfig


class ExcelToMySQLConverterV3:
    """Excel报告转MySQL转换器 V3"""

    def __init__(self, excel_dir: str = None, db_config: DatabaseConfig = None):
        """
        初始化转换器

        Args:
            excel_dir: Excel文件目录路径，如果为None则使用默认路径
            db_config: 数据库配置，如果为None则使用默认配置
        """
        if excel_dir is None:
            # 使用默认的Excel目录
            self.excel_dir = current_dir / "allure_report_excel"
        else:
            self.excel_dir = Path(excel_dir)

        # 初始化数据库管理器
        self.db_manager = DatabaseManager(db_config)
        
        print(f"📁 Excel文件目录: {self.excel_dir}")

    def _load_device_info(self) -> Dict[str, Any]:
        """
        从devices.yaml加载当前设备信息

        Returns:
            Dict[str, Any]: 设备信息字典
        """
        try:
            devices_config_path = project_root / "config" / "devices.yaml"
            with open(devices_config_path, 'r', encoding='utf-8') as f:
                devices_config = yaml.safe_load(f)

            current_device_key = devices_config.get('current_device', 'current_device')
            device_info = devices_config.get('devices', {}).get(current_device_key, {})

            return device_info
        except Exception as e:
            print(f"加载设备信息失败: {e}")
            return {
                'device_id': 'Unknown',
                'device_name': 'Unknown Device',
                'android_version': 'Unknown',
                'brand': 'Unknown',
                'model': 'Unknown',
                'platform_version': 'Unknown'
            }

    def _parse_excel_filename(self, filename: str) -> Optional[datetime]:
        """
        从Excel文件名解析执行时间

        Args:
            filename: Excel文件名，格式如 allure_report_25-09-04_11-18-55.xlsx

        Returns:
            datetime: 解析出的执行时间，解析失败返回None
        """
        try:
            # 提取时间部分：25-09-04_11-18-55
            pattern = r'allure_report_(\d{2}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})\.xlsx'
            match = re.search(pattern, filename)
            
            if match:
                time_str = match.group(1)
                # 转换为标准格式：2025-09-04 11:18:55
                # 原格式：25-09-04_11-18-55
                date_part, time_part = time_str.split('_')
                
                # 处理日期部分：25-09-04 -> 2025-09-04
                year, month, day = date_part.split('-')
                date_formatted = f"20{year}-{month}-{day}"
                
                # 处理时间部分：11-18-55 -> 11:18:55
                time_formatted = time_part.replace('-', ':')
                
                # 组合最终格式
                final_time_str = f"{date_formatted} {time_formatted}"
                
                return datetime.strptime(final_time_str, '%Y-%m-%d %H:%M:%S')
            else:
                print(f"无法解析文件名时间: {filename}")
                return None
                
        except Exception as e:
            print(f"解析文件名时间失败: {filename}, 错误: {e}")
            return None

    def _determine_command_type(self, row: pd.Series) -> str:
        """
        判断指令类型（文本或语音）
        
        Args:
            row: DataFrame行数据，包含标签信息
            
        Returns:
            'text' 或 'voice'
        """
        # 根据标签_parentSuite字段判断指令类型
        parent_suite_col = '标签_parentSuite'
        if parent_suite_col in row and pd.notna(row[parent_suite_col]):
            parent_suite = str(row[parent_suite_col])
            if parent_suite == 'testcases.test_voice_ella':
                return 'voice'
        
        # 默认为文本指令
        return 'text'

    def _extract_label_values_from_row(self, row: pd.Series) -> Dict[str, str]:
        """
        从Excel行数据中提取标签值

        Args:
            row: DataFrame行数据

        Returns:
            Dict[str, str]: 完整标签值字典
        """
        label_values = {
            'parent_suite': None,
            'suite': None,
            'sub_suite': None,
            'feature': None,
            'story': None,
            'severity': None,
            'tag': None,
            'host': None,
            'thread': None,
            'framework': None,
            'language': None,
            'package': None,
            'epic': None
        }

        # 检查所有标签列
        label_mappings = {
            '标签_parentSuite': 'parent_suite',
            '标签_suite': 'suite',
            '标签_subSuite': 'sub_suite',
            '标签_feature': 'feature',
            '标签_story': 'story',
            '标签_severity': 'severity',
            '标签_tag': 'tag',
            '标签_host': 'host',
            '标签_thread': 'thread',
            '标签_framework': 'framework',
            '标签_language': 'language',
            '标签_package': 'package',
            '标签_epic': 'epic'
        }

        for excel_col, db_field in label_mappings.items():
            if excel_col in row and pd.notna(row[excel_col]):
                label_values[db_field] = str(row[excel_col])

        return label_values

    def _check_duplicate_data(self, suite: str, start_time: datetime, device_id: str) -> bool:
        """
        检查是否存在重复数据

        Args:
            suite: suite标签值
            start_time: 开始时间
            device_id: 设备ID

        Returns:
            bool: 存在重复数据返回True，不存在返回False
        """
        if not suite or not start_time:
            return False

        try:
            with self.db_manager.get_cursor() as cursor:
                # 查询相同suite、start_time和device_id的记录
                cursor.execute("""
                    SELECT COUNT(*) as count
                    FROM test_results
                    WHERE suite = %s
                    AND start_time = %s
                    AND device_id = %s
                    AND data_source = 'excel_import'
                """, (suite, start_time, device_id))

                result = cursor.fetchone()
                return result['count'] > 0

        except Exception as e:
            print(f"⚠️ 检查重复数据失败: {e}")
            return False

    def _save_test_results_from_excel(self, execution_id: str, device_info: Dict[str, Any],
                                     execution_time: datetime, excel_filename: str,
                                     df_results: pd.DataFrame) -> bool:
        """
        从Excel数据保存测试结果到核心表

        Args:
            execution_id: 执行批次ID
            device_info: 设备信息
            execution_time: 执行时间
            excel_filename: Excel文件名
            df_results: 测试用例结果DataFrame

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        if df_results.empty:
            print("⚠️ 没有测试用例结果需要保存")
            return True

        try:
            sql = """
            INSERT INTO test_results (
                execution_id, test_case_uuid, test_case_name, test_status, command_type,
                device_id, device_name, device_brand, device_model, android_version, platform_version,
                execution_date, execution_time, start_time, end_time, duration_ms,
                description, full_name, history_id, test_case_id, failure_message, failure_trace, failure_reason,
                steps_count, steps_details, attachments_count, attachments_details, parameters_count, parameters_details,
                parent_suite, suite, sub_suite, feature, story, severity, tag, host, thread, framework, language, package, epic,
                data_source, excel_file_name
            ) VALUES (
                %(execution_id)s, %(test_case_uuid)s, %(test_case_name)s, %(test_status)s, %(command_type)s,
                %(device_id)s, %(device_name)s, %(device_brand)s, %(device_model)s, %(android_version)s, %(platform_version)s,
                %(execution_date)s, %(execution_time)s, %(start_time)s, %(end_time)s, %(duration_ms)s,
                %(description)s, %(full_name)s, %(history_id)s, %(test_case_id)s, %(failure_message)s, %(failure_trace)s, %(failure_reason)s,
                %(steps_count)s, %(steps_details)s, %(attachments_count)s, %(attachments_details)s, %(parameters_count)s, %(parameters_details)s,
                %(parent_suite)s, %(suite)s, %(sub_suite)s, %(feature)s, %(story)s, %(severity)s, %(tag)s, %(host)s, %(thread)s, %(framework)s, %(language)s, %(package)s, %(epic)s,
                %(data_source)s, %(excel_file_name)s
            )
            """

            inserted_count = 0
            duplicate_count = 0

            with self.db_manager.get_cursor() as cursor:
                for _, row in df_results.iterrows():
                    # 处理时间字段
                    start_time = None
                    end_time = None
                    try:
                        if pd.notna(row.get('开始时间')):
                            start_time = pd.to_datetime(row['开始时间'])
                        if pd.notna(row.get('结束时间')):
                            end_time = pd.to_datetime(row['结束时间'])
                    except:
                        pass

                    # 处理数值字段
                    duration_ms = None
                    steps_count = 0
                    attachments_count = 0
                    parameters_count = 0

                    try:
                        if pd.notna(row.get('执行时长(ms)')):
                            duration_ms = int(row['执行时长(ms)'])
                        if pd.notna(row.get('步骤数量')):
                            steps_count = int(row['步骤数量'])
                        if pd.notna(row.get('附件数量')):
                            attachments_count = int(row['附件数量'])
                        if pd.notna(row.get('参数数量')):
                            parameters_count = int(row['参数数量'])
                    except:
                        pass

                    # 判断指令类型
                    command_type = self._determine_command_type(row)

                    # 提取标签值
                    label_values = self._extract_label_values_from_row(row)

                    # 检查重复数据
                    suite = label_values['suite']
                    device_id = device_info.get('device_id', 'Unknown')

                    if self._check_duplicate_data(suite, start_time, device_id):
                        duplicate_count += 1
                        continue  # 跳过重复数据
                    
                    # 组装数据
                    test_result_data = {
                        'execution_id': execution_id,
                        'test_case_uuid': str(row.get('UUID', '')),
                        'test_case_name': str(row.get('测试用例名称', ''))[:500],  # 限制长度
                        'test_status': str(row.get('测试状态', '')),
                        'command_type': command_type,
                        
                        # 设备信息
                        'device_id': device_info.get('device_id', 'Unknown'),
                        'device_name': device_info.get('device_name', 'Unknown Device'),
                        'device_brand': device_info.get('brand', 'Unknown'),
                        'device_model': device_info.get('model', 'Unknown'),
                        'android_version': device_info.get('android_version', 'Unknown'),
                        'platform_version': device_info.get('platform_version', 'Unknown'),
                        
                        # 执行信息
                        'execution_date': execution_time.date(),
                        'execution_time': execution_time,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration_ms': duration_ms,
                        
                        # 测试详情
                        'description': str(row.get('描述', '')) if pd.notna(row.get('描述')) else None,
                        'full_name': str(row.get('完整名称', ''))[:1000] if pd.notna(row.get('完整名称')) else None,
                        'history_id': str(row.get('历史ID', '')) if pd.notna(row.get('历史ID')) else None,
                        'test_case_id': str(row.get('测试用例ID', '')) if pd.notna(row.get('测试用例ID')) else None,
                        'failure_message': str(row.get('失败消息', '')) if pd.notna(row.get('失败消息')) else None,
                        'failure_trace': str(row.get('失败堆栈', '')) if pd.notna(row.get('失败堆栈')) else None,
                        'failure_reason': str(row.get('失败原因', '')) if pd.notna(row.get('失败原因')) else None,

                        # 步骤和附件信息
                        'steps_count': steps_count,
                        'steps_details': str(row.get('步骤详情', '')) if pd.notna(row.get('步骤详情')) else None,
                        'attachments_count': attachments_count,
                        'attachments_details': str(row.get('附件详情', '')) if pd.notna(row.get('附件详情')) else None,
                        'parameters_count': parameters_count,
                        'parameters_details': str(row.get('参数详情', '')) if pd.notna(row.get('参数详情')) else None,

                        # 标签信息（完整）
                        'parent_suite': label_values['parent_suite'],
                        'suite': label_values['suite'],
                        'sub_suite': label_values['sub_suite'],
                        'feature': label_values['feature'],
                        'story': label_values['story'],
                        'severity': label_values['severity'],
                        'tag': label_values['tag'],
                        'host': label_values['host'],
                        'thread': label_values['thread'],
                        'framework': label_values['framework'],
                        'language': label_values['language'],
                        'package': label_values['package'],
                        'epic': label_values['epic'],
                        
                        # 元数据
                        'data_source': 'excel_import',
                        'excel_file_name': excel_filename
                    }

                    cursor.execute(sql, test_result_data)
                    inserted_count += 1

            # 显示保存结果统计
            total_records = len(df_results)
            print(f"✅ 测试结果保存完成:")
            print(f"   📊 总记录数: {total_records}")
            print(f"   ✅ 新增记录: {inserted_count}")
            print(f"   🔄 重复跳过: {duplicate_count}")

            if duplicate_count > 0:
                print(f"   📈 去重率: {duplicate_count/total_records*100:.1f}%")

            return True

        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")
            return False

    def _is_valid_excel_file(self, excel_file: Path) -> bool:
        """
        验证Excel文件是否有效

        Args:
            excel_file: Excel文件路径

        Returns:
            bool: 文件有效返回True，无效返回False
        """
        try:
            # 检查文件大小
            if excel_file.stat().st_size == 0:
                print(f"⚠️ 文件为空: {excel_file.name}")
                return False

            # 检查文件扩展名
            if not excel_file.name.endswith('.xlsx'):
                print(f"⚠️ 不是Excel文件: {excel_file.name}")
                return False

            # 检查是否是临时文件
            if excel_file.name.startswith('~$'):
                print(f"⚠️ 跳过临时文件: {excel_file.name}")
                return False

            # 尝试读取Excel文件结构
            try:
                xl = pd.ExcelFile(excel_file)
                sheet_names = xl.sheet_names
                xl.close()

                # 检查是否包含必要的工作表
                if '测试用例结果' not in sheet_names:
                    print(f"⚠️ 文件中没有'测试用例结果'工作表: {excel_file.name}")
                    return False

                return True

            except Exception as e:
                print(f"⚠️ Excel文件格式错误: {excel_file.name}, 错误: {e}")
                return False

        except Exception as e:
            print(f"⚠️ 文件验证失败: {excel_file.name}, 错误: {e}")
            return False

    def _process_excel_file(self, excel_file: Path) -> bool:
        """
        处理单个Excel文件

        Args:
            excel_file: Excel文件路径

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        try:
            print(f"\n📊 处理Excel文件: {excel_file.name}")

            # 验证文件有效性
            if not self._is_valid_excel_file(excel_file):
                return False

            # 解析文件名获取执行时间
            execution_time = self._parse_excel_filename(excel_file.name)
            if not execution_time:
                print(f"❌ 无法解析文件名时间，跳过文件: {excel_file.name}")
                return False

            # 生成执行批次ID
            execution_id = str(uuid.uuid4())

            # 加载设备信息
            device_info = self._load_device_info()

            # 读取Excel文件
            try:
                df_results = pd.read_excel(excel_file, sheet_name='测试用例结果')

                # 检查数据是否为空
                if df_results.empty:
                    print(f"⚠️ Excel文件中没有测试数据: {excel_file.name}")
                    return False

                # 保存测试结果
                if not self._save_test_results_from_excel(execution_id, device_info, execution_time, excel_file.name, df_results):
                    print(f"❌ 保存测试结果失败")
                    return False

            except Exception as e:
                print(f"❌ 读取Excel文件失败: {excel_file.name}, 错误: {e}")
                return False

            print(f"✅ Excel文件处理成功: {excel_file.name}")
            return True

        except Exception as e:
            print(f"❌ 处理Excel文件失败: {excel_file.name}, 错误: {e}")
            return False

    def convert_all_excel_files(self) -> bool:
        """
        转换所有Excel文件

        Returns:
            bool: 转换成功返回True，失败返回False
        """
        print("开始转换Excel文件到数据库...")

        try:
            # 测试数据库连接
            if not self.db_manager.test_connection():
                print("❌ 数据库连接失败")
                return False

            # 获取所有Excel文件
            excel_files = list(self.excel_dir.glob("*.xlsx"))
            if not excel_files:
                print(f"❌ 在目录 {self.excel_dir} 中没有找到Excel文件")
                return False

            # 过滤掉临时文件
            valid_files = [f for f in excel_files if not f.name.startswith('~$')]
            print(f"📊 找到 {len(valid_files)} 个有效Excel文件（总共 {len(excel_files)} 个文件）")

            success_count = 0
            failed_count = 0
            skipped_count = 0
            failed_files = []
            total_processed_records = 0
            total_duplicate_records = 0

            for excel_file in sorted(valid_files):
                try:
                    if self._process_excel_file(excel_file):
                        success_count += 1
                        # 这里可以添加记录统计，但需要修改_process_excel_file返回值
                    else:
                        failed_count += 1
                        failed_files.append(excel_file.name)
                except Exception as e:
                    print(f"❌ 处理文件异常: {excel_file.name}, 错误: {e}")
                    failed_count += 1
                    failed_files.append(excel_file.name)

            print(f"\n📊 转换结果汇总:")
            print(f"  ✅ 成功: {success_count}")
            print(f"  ❌ 失败: {failed_count}")
            if len(excel_files) > len(valid_files):
                skipped_count = len(excel_files) - len(valid_files)
                print(f"  ⏭️ 跳过: {skipped_count} (临时文件)")

            if valid_files:
                print(f"  📈 成功率: {success_count/len(valid_files)*100:.1f}%")

            # 显示失败的文件列表
            if failed_files:
                print(f"\n❌ 失败的文件:")
                for failed_file in failed_files:
                    print(f"  - {failed_file}")

            if failed_count == 0:
                print("\n🎉 所有Excel文件转换成功！")
                return True
            else:
                print(f"\n⚠️ 有 {failed_count} 个文件转换失败")
                return success_count > 0

        except Exception as e:
            print(f"❌ 转换过程中发生错误: {e}")
            return False

    def initialize_database(self) -> bool:
        """
        初始化数据库（创建数据库和表）

        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        print("开始初始化数据库...")

        try:
            # 创建数据库
            if not self.db_manager.create_database_if_not_exists():
                print("❌ 创建数据库失败")
                return False

            # 执行表结构SQL
            schema_file = Path(__file__).parent / "database_schema_v2.sql"
            if not self.db_manager.execute_sql_file(str(schema_file)):
                print("❌ 创建数据库表失败")
                return False

            print("✅ 数据库初始化成功")
            return True

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            return False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='将Excel报告转换并存储到MySQL数据库 V3')
    parser.add_argument('--excel-dir', '-e', default=None,
                        help='Excel文件目录路径 (默认: tools/allure_report_analysis/allure_report_excel)')
    parser.add_argument('--init-db', action='store_true',
                        help='初始化数据库（创建数据库和表）')
    parser.add_argument('--db-host', default='localhost',
                        help='数据库主机地址 (默认: localhost)')
    parser.add_argument('--db-port', type=int, default=3306,
                        help='数据库端口 (默认: 3306)')
    parser.add_argument('--db-user', default='root',
                        help='数据库用户名 (默认: root)')
    parser.add_argument('--db-password', default='root123456',
                        help='数据库密码 (默认: root123456)')
    parser.add_argument('--db-name', default='ella_allure_report',
                        help='数据库名称 (默认: ella_allure_report)')

    args = parser.parse_args()

    # 创建数据库配置
    db_config = DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        username=args.db_user,
        password=args.db_password,
        database=args.db_name
    )

    # 创建转换器
    converter = ExcelToMySQLConverterV3(args.excel_dir, db_config)

    print(f"📁 Excel文件目录: {converter.excel_dir}")
    print(f"🗄️ 数据库配置: {db_config.username}@{db_config.host}:{db_config.port}/{db_config.database}")

    try:
        # 如果指定了初始化数据库选项
        if args.init_db:
            if not converter.initialize_database():
                print("\n❌ 数据库初始化失败")
                return 1
            print("\n✅ 数据库初始化完成")

        # 执行转换
        if converter.convert_all_excel_files():
            print(f"\n✅ 转换完成！数据已保存到数据库")
        else:
            print(f"\n❌ 转换失败")
            return 1

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
