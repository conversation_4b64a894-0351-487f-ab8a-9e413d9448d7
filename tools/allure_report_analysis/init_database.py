#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于创建Ella Allure测试报告数据库和相关表结构
"""

import sys
from pathlib import Path
import argparse

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from tools.allure_report_analysis.database_config import DatabaseManager, DatabaseConfig


def init_database(db_config: DatabaseConfig = None) -> bool:
    """
    初始化数据库
    
    Args:
        db_config: 数据库配置，如果为None则使用默认配置
        
    Returns:
        bool: 初始化成功返回True，失败返回False
    """
    if db_config is None:
        db_config = DatabaseConfig()
    
    print("🚀 开始初始化Ella Allure测试报告数据库...")
    print(f"📊 数据库配置: {db_config.username}@{db_config.host}:{db_config.port}/{db_config.database}")
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager(db_config)
        
        # 1. 创建数据库
        print("\n📝 步骤1: 创建数据库...")
        if not db_manager.create_database_if_not_exists():
            print("❌ 创建数据库失败")
            return False
        print(f"✅ 数据库 '{db_config.database}' 创建成功或已存在")
        
        # 2. 测试连接
        print("\n🔗 步骤2: 测试数据库连接...")
        if not db_manager.test_connection():
            print("❌ 数据库连接测试失败")
            return False
        print("✅ 数据库连接测试成功")
        
        # 3. 创建表结构
        print("\n🏗️ 步骤3: 创建数据库表结构...")
        schema_file = Path(__file__).parent / "database_schema.sql"
        if not schema_file.exists():
            print(f"❌ 数据库表结构文件不存在: {schema_file}")
            return False
            
        if not db_manager.execute_sql_file(str(schema_file)):
            print("❌ 创建数据库表结构失败")
            return False
        print("✅ 数据库表结构创建成功")
        
        # 4. 验证表结构
        print("\n🔍 步骤4: 验证表结构...")
        tables_to_check = [
            'test_executions',
            'test_case_results', 
            'test_case_labels',
            'test_containers'
        ]
        
        with db_manager.get_cursor() as cursor:
            for table_name in tables_to_check:
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                result = cursor.fetchone()
                if result:
                    print(f"  ✅ 表 '{table_name}' 存在")
                else:
                    print(f"  ❌ 表 '{table_name}' 不存在")
                    return False
        
        print("\n🎉 数据库初始化完成！")
        print("\n📋 数据库表结构:")
        print("  - test_executions: 测试执行记录表")
        print("  - test_case_results: 测试用例结果表")
        print("  - test_case_labels: 测试用例标签表")
        print("  - test_containers: 测试容器信息表")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {e}")
        return False


def show_database_info(db_config: DatabaseConfig = None):
    """
    显示数据库信息
    
    Args:
        db_config: 数据库配置，如果为None则使用默认配置
    """
    if db_config is None:
        db_config = DatabaseConfig()
    
    print("📊 数据库配置信息:")
    print(f"  主机地址: {db_config.host}")
    print(f"  端口: {db_config.port}")
    print(f"  用户名: {db_config.username}")
    print(f"  数据库名: {db_config.database}")
    print(f"  字符集: {db_config.charset}")
    
    try:
        db_manager = DatabaseManager(db_config)
        
        if db_manager.test_connection():
            print("  连接状态: ✅ 正常")
            
            # 显示表信息
            with db_manager.get_cursor() as cursor:
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                if tables:
                    print(f"\n📋 数据库表列表 (共 {len(tables)} 个表):")
                    for table in tables:
                        table_name = list(table.values())[0]
                        
                        # 获取表记录数
                        cursor.execute(f"SELECT COUNT(*) as count FROM `{table_name}`")
                        count_result = cursor.fetchone()
                        record_count = count_result['count'] if count_result else 0
                        
                        print(f"  - {table_name}: {record_count} 条记录")
                else:
                    print("\n📋 数据库中没有表")
        else:
            print("  连接状态: ❌ 失败")
            
    except Exception as e:
        print(f"  连接状态: ❌ 错误 - {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Ella Allure测试报告数据库初始化工具')
    parser.add_argument('--init', action='store_true',
                        help='初始化数据库（创建数据库和表）')
    parser.add_argument('--info', action='store_true',
                        help='显示数据库信息')
    parser.add_argument('--db-host', default='localhost',
                        help='数据库主机地址 (默认: localhost)')
    parser.add_argument('--db-port', type=int, default=3306,
                        help='数据库端口 (默认: 3306)')
    parser.add_argument('--db-user', default='root',
                        help='数据库用户名 (默认: root)')
    parser.add_argument('--db-password', default='root123456',
                        help='数据库密码 (默认: root123456)')
    parser.add_argument('--db-name', default='ella_allure_report',
                        help='数据库名称 (默认: ella_allure_report)')

    args = parser.parse_args()

    # 创建数据库配置
    db_config = DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        username=args.db_user,
        password=args.db_password,
        database=args.db_name
    )

    # 如果没有指定任何操作，默认显示信息
    if not args.init and not args.info:
        args.info = True

    try:
        if args.init:
            if init_database(db_config):
                print("\n✅ 数据库初始化成功")
                return 0
            else:
                print("\n❌ 数据库初始化失败")
                return 1
        
        if args.info:
            show_database_info(db_config)
            return 0

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
