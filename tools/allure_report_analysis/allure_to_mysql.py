#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Allure报告转MySQL数据库工具
将allure报告中的用例执行结果信息存储到MySQL数据库中
"""

import json
import os
import uuid
import yaml
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from tools.allure_report_analysis.database_config import DatabaseManager, DatabaseConfig


class AllureToMySQLConverter:
    """Allure报告转MySQL转换器"""

    def __init__(self, reports_dir: str = None, db_config: DatabaseConfig = None):
        """
        初始化转换器

        Args:
            reports_dir: 报告目录路径，如果为None则自动检测项目根目录下的reports
            db_config: 数据库配置，如果为None则使用默认配置
        """
        if reports_dir is None:
            # 自动检测项目根目录下的reports目录
            current_file_dir = Path(__file__).parent  # tools/allure_report_analysis
            project_root = current_file_dir.parent.parent  # 项目根目录
            self.reports_dir = project_root / "reports"
        else:
            self.reports_dir = Path(reports_dir)

        self.allure_results_dir = self.reports_dir / "allure-results"
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager(db_config)
        
        # 生成执行批次ID
        self.execution_id = str(uuid.uuid4())
        
        print(f"📁 Allure结果目录: {self.allure_results_dir}")
        print(f"🔑 执行批次ID: {self.execution_id}")

    def _read_json_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """
        读取JSON文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            解析后的JSON数据，如果读取失败返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError, UnicodeDecodeError) as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return None

    def _format_timestamp(self, timestamp: Optional[int]) -> Optional[datetime]:
        """
        格式化时间戳
        
        Args:
            timestamp: 毫秒时间戳
            
        Returns:
            datetime对象，如果时间戳无效返回None
        """
        if timestamp is None:
            return None
        try:
            # Allure使用毫秒时间戳
            return datetime.fromtimestamp(timestamp / 1000)
        except (ValueError, OSError):
            return None

    def _calculate_duration(self, start: Optional[int], stop: Optional[int]) -> Optional[int]:
        """
        计算执行时长

        Args:
            start: 开始时间戳
            stop: 结束时间戳

        Returns:
            执行时长（毫秒），如果时间戳无效返回None
        """
        if start is None or stop is None:
            return None
        return stop - start

    def _extract_failure_reason(self, status_details: Dict[str, Any]) -> str:
        """
        提取失败原因的简要描述

        Args:
            status_details: 状态详情字典

        Returns:
            失败原因的简要描述
        """
        if not status_details:
            return ''

        message = status_details.get('message', '')
        if not message:
            return ''

        # 提取AssertionError的主要信息
        if 'AssertionError:' in message:
            # 提取AssertionError后的第一行作为主要错误信息
            lines = message.split('\n')
            for line in lines:
                if 'AssertionError:' in line:
                    # 去掉"AssertionError: "前缀
                    reason = line.replace('AssertionError:', '').strip()
                    return reason

        # 提取其他异常类型的信息
        if ':' in message:
            # 取第一行的异常信息
            first_line = message.split('\n')[0]
            if ':' in first_line:
                return first_line.split(':', 1)[1].strip()

        # 如果没有特殊格式，返回第一行
        return message.split('\n')[0].strip()

    def _extract_steps_info(self, steps: List[Dict[str, Any]]) -> str:
        """
        提取步骤信息

        Args:
            steps: 步骤列表

        Returns:
            步骤信息字符串
        """
        if not steps:
            return ''

        step_info = []
        for i, step in enumerate(steps, 1):
            step_name = step.get('name', f'步骤{i}')
            step_status = step.get('status', 'unknown')

            # 如果步骤失败，添加失败信息
            if step_status in ['failed', 'broken']:
                step_details = step.get('statusDetails', {})
                if step_details:
                    failure_reason = self._extract_failure_reason(step_details)
                    if failure_reason:
                        step_info.append(f"{i}. {step_name} ({step_status}) - {failure_reason}")
                    else:
                        step_info.append(f"{i}. {step_name} ({step_status})")
                else:
                    step_info.append(f"{i}. {step_name} ({step_status})")
            else:
                step_info.append(f"{i}. {step_name} ({step_status})")

        return '; '.join(step_info)

    def _extract_attachments_info(self, attachments: List[Dict[str, Any]]) -> str:
        """
        提取附件信息
        
        Args:
            attachments: 附件列表
            
        Returns:
            附件信息字符串
        """
        if not attachments:
            return ''

        attachment_info = []
        for attachment in attachments:
            name = attachment.get('name', '未知附件')
            type_info = attachment.get('type', '未知类型')
            attachment_info.append(f"{name} ({type_info})")

        return '; '.join(attachment_info)

    def _extract_parameters_info(self, parameters: List[Dict[str, Any]]) -> str:
        """
        提取参数信息
        
        Args:
            parameters: 参数列表
            
        Returns:
            参数信息字符串
        """
        if not parameters:
            return ''

        param_info = []
        for param in parameters:
            name = param.get('name', '未知参数')
            value = param.get('value', '')
            param_info.append(f"{name}={value}")

        return '; '.join(param_info)

    def _load_device_info(self) -> Dict[str, Any]:
        """
        从devices.yaml加载当前设备信息

        Returns:
            Dict[str, Any]: 设备信息字典
        """
        try:
            devices_config_path = project_root / "config" / "devices.yaml"
            with open(devices_config_path, 'r', encoding='utf-8') as f:
                devices_config = yaml.safe_load(f)

            current_device_key = devices_config.get('current_device', 'current_device')
            device_info = devices_config.get('devices', {}).get(current_device_key, {})

            return device_info
        except Exception as e:
            print(f"加载设备信息失败: {e}")
            return {
                'device_id': 'Unknown',
                'device_name': 'Unknown Device',
                'android_version': 'Unknown',
                'brand': 'Unknown',
                'model': 'Unknown',
                'platform_version': 'Unknown'
            }

    def _determine_command_type(self, labels: List[Dict[str, Any]]) -> str:
        """
        判断指令类型（文本或语音）

        Args:
            labels: 测试用例标签列表

        Returns:
            'text' 或 'voice'
        """
        # 根据标签_parentSuite字段判断指令类型
        for label in labels:
            if label.get('name') == 'parentSuite':
                parent_suite = label.get('value', '')
                if parent_suite == 'testcases.test_voice_ella':
                    return 'voice'

        # 默认为文本指令
        return 'text'

    def _get_test_statistics(self) -> Dict[str, Any]:
        """
        从summary.json获取测试统计信息

        Returns:
            Dict[str, Any]: 包含测试统计信息的字典
        """
        try:
            # 构建summary.json文件路径
            summary_path = Path(self.reports_dir) / "allure-report" / "widgets" / "summary.json"

            if not summary_path.exists():
                print(f"警告: summary.json文件不存在: {summary_path}")
                return self._get_default_statistics()

            # 读取summary.json文件
            with open(summary_path, 'r', encoding='utf-8') as f:
                summary_data = json.load(f)

            # 提取统计信息
            statistic = summary_data.get('statistic', {})
            time_info = summary_data.get('time', {})

            total_cases = statistic.get('total', 0)
            passed_count = statistic.get('passed', 0)
            failed_count = statistic.get('failed', 0)
            broken_count = statistic.get('broken', 0)
            skipped_count = statistic.get('skipped', 0)
            unknown_count = statistic.get('unknown', 0)

            # 计算成功率（通过的用例 / 总用例）
            success_rate = (passed_count / (total_cases - skipped_count - unknown_count) * 100) if total_cases > 0 else 0

            # 时间信息
            start_timestamp = time_info.get('start', 0) / 1000 if time_info.get('start') else None
            stop_timestamp = time_info.get('stop', 0) / 1000 if time_info.get('stop') else None
            duration_ms = time_info.get('duration', 0)

            return {
                'total_cases': total_cases,
                'passed_count': passed_count,
                'failed_count': failed_count,
                'broken_count': broken_count,
                'skipped_count': skipped_count,
                'unknown_count': unknown_count,
                'success_rate': round(success_rate, 2),
                'start_timestamp': start_timestamp,
                'stop_timestamp': stop_timestamp,
                'duration_ms': duration_ms
            }

        except Exception as e:
            print(f"从summary.json获取统计信息失败: {e}")
            return self._get_default_statistics()

    def _get_default_statistics(self) -> Dict[str, Any]:
        """
        获取默认统计信息

        Returns:
            Dict[str, Any]: 默认统计信息
        """
        return {
            'total_cases': 0,
            'passed_count': 0,
            'failed_count': 0,
            'broken_count': 0,
            'skipped_count': 0,
            'unknown_count': 0,
            'success_rate': 0.0,
            'start_timestamp': None,
            'stop_timestamp': None,
            'duration_ms': 0
        }

    def _extract_test_results(self) -> List[Dict[str, Any]]:
        """
        从allure-results目录提取测试结果

        Returns:
            测试结果列表
        """
        test_results = []

        # 检查allure-results目录
        if not self.allure_results_dir.exists():
            print(f"❌ Allure结果目录不存在: {self.allure_results_dir}")
            return test_results

        print(f"✅ 找到Allure结果目录: {self.allure_results_dir}")

        # 读取所有result.json文件
        result_files = list(self.allure_results_dir.glob("*-result.json"))
        print(f"找到 {len(result_files)} 个测试结果文件")

        for result_file in result_files:
            result_data = self._read_json_file(result_file)
            if result_data:
                # 提取基本信息
                test_case = {
                    'uuid': result_data.get('uuid', ''),
                    'test_case_name': result_data.get('name', ''),
                    'test_status': result_data.get('status', ''),
                    'description': result_data.get('description', ''),
                    'full_name': result_data.get('fullName', ''),
                    'history_id': result_data.get('historyId', ''),
                    'test_case_id': result_data.get('testCaseId', ''),
                    'start_time': self._format_timestamp(result_data.get('start')),
                    'end_time': self._format_timestamp(result_data.get('stop')),
                    'duration_ms': self._calculate_duration(result_data.get('start'), result_data.get('stop')),
                }

                # 提取失败原因信息
                status_details = result_data.get('statusDetails', {})
                test_case['failure_message'] = status_details.get('message', '')
                test_case['failure_trace'] = status_details.get('trace', '')
                test_case['failure_reason'] = self._extract_failure_reason(status_details)

                # 提取标签信息（暂时存储，后续单独处理）
                labels = result_data.get('labels', [])
                test_case['labels'] = labels

                # 提取步骤信息
                steps = result_data.get('steps', [])
                test_case['steps_count'] = len(steps)
                test_case['steps_details'] = self._extract_steps_info(steps)

                # 提取附件信息
                attachments = result_data.get('attachments', [])
                test_case['attachments_count'] = len(attachments)
                test_case['attachments_details'] = self._extract_attachments_info(attachments)

                # 提取参数信息
                parameters = result_data.get('parameters', [])
                test_case['parameters_count'] = len(parameters)
                test_case['parameters_details'] = self._extract_parameters_info(parameters)

                # 判断指令类型（基于标签信息）
                test_case['command_type'] = self._determine_command_type(labels)

                test_results.append(test_case)

        return test_results



    def _extract_label_values(self, labels: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        提取关键标签值

        Args:
            labels: 标签列表

        Returns:
            Dict[str, str]: 关键标签值字典
        """
        label_values = {
            'parent_suite': None,
            'suite': None,
            'sub_suite': None,
            'feature': None,
            'story': None
        }

        for label in labels:
            label_name = label.get('name', '')
            label_value = label.get('value', '')

            if label_name == 'parentSuite':
                label_values['parent_suite'] = label_value
            elif label_name == 'suite':
                label_values['suite'] = label_value
            elif label_name == 'subSuite':
                label_values['sub_suite'] = label_value
            elif label_name == 'feature':
                label_values['feature'] = label_value
            elif label_name == 'story':
                label_values['story'] = label_value

        return label_values

    def _save_test_results(self, test_results: List[Dict[str, Any]], device_info: Dict[str, Any]) -> bool:
        """
        保存测试结果到核心表

        Args:
            test_results: 测试结果列表
            device_info: 设备信息

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        if not test_results:
            print("⚠️ 没有测试用例结果需要保存")
            return True

        try:
            sql = """
            INSERT INTO test_results (
                execution_id, test_case_uuid, test_case_name, test_status, command_type,
                device_id, device_name, device_brand, device_model, android_version, platform_version,
                execution_date, execution_time, start_time, end_time, duration_ms,
                description, full_name, failure_message, failure_reason,
                parent_suite, suite, sub_suite, feature, story,
                data_source
            ) VALUES (
                %(execution_id)s, %(test_case_uuid)s, %(test_case_name)s, %(test_status)s, %(command_type)s,
                %(device_id)s, %(device_name)s, %(device_brand)s, %(device_model)s, %(android_version)s, %(platform_version)s,
                %(execution_date)s, %(execution_time)s, %(start_time)s, %(end_time)s, %(duration_ms)s,
                %(description)s, %(full_name)s, %(failure_message)s, %(failure_reason)s,
                %(parent_suite)s, %(suite)s, %(sub_suite)s, %(feature)s, %(story)s,
                %(data_source)s
            )
            """

            with self.db_manager.get_cursor() as cursor:
                for test_case in test_results:
                    # 提取标签值
                    labels = test_case.get('labels', [])
                    label_values = self._extract_label_values(labels)

                    # 组装数据
                    test_result_data = {
                        'execution_id': self.execution_id,
                        'test_case_uuid': test_case.get('uuid', ''),
                        'test_case_name': test_case.get('test_case_name', ''),
                        'test_status': test_case.get('test_status', ''),
                        'command_type': test_case.get('command_type', 'text'),

                        # 设备信息
                        'device_id': device_info.get('device_id', 'Unknown'),
                        'device_name': device_info.get('device_name', 'Unknown Device'),
                        'device_brand': device_info.get('brand', 'Unknown'),
                        'device_model': device_info.get('model', 'Unknown'),
                        'android_version': device_info.get('android_version', 'Unknown'),
                        'platform_version': device_info.get('platform_version', 'Unknown'),

                        # 执行信息
                        'execution_date': datetime.now().date(),
                        'execution_time': datetime.now(),
                        'start_time': test_case.get('start_time'),
                        'end_time': test_case.get('end_time'),
                        'duration_ms': test_case.get('duration_ms'),

                        # 测试详情
                        'description': test_case.get('description'),
                        'full_name': test_case.get('full_name'),
                        'failure_message': test_case.get('failure_message'),
                        'failure_reason': test_case.get('failure_reason'),

                        # 标签信息
                        'parent_suite': label_values['parent_suite'],
                        'suite': label_values['suite'],
                        'sub_suite': label_values['sub_suite'],
                        'feature': label_values['feature'],
                        'story': label_values['story'],

                        # 元数据
                        'data_source': 'allure_json'
                    }

                    cursor.execute(sql, test_result_data)

            print(f"✅ 测试结果保存成功，共 {len(test_results)} 条记录")
            return True

        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")
            return False



    def convert_to_database(self) -> bool:
        """
        将allure报告转换并存储到数据库

        Returns:
            bool: 转换成功返回True，失败返回False
        """
        print("开始将Allure报告数据转换并存储到数据库...")

        try:
            # 测试数据库连接
            if not self.db_manager.test_connection():
                print("❌ 数据库连接失败")
                return False

            # 加载设备信息
            device_info = self._load_device_info()
            print(f"📱 当前设备: {device_info.get('device_name', 'Unknown')} ({device_info.get('device_id', 'Unknown')})")

            # 获取测试统计信息
            statistics = self._get_test_statistics()
            print(f"📊 测试统计: 总计 {statistics['total_cases']} 个用例，通过 {statistics['passed_count']} 个，失败 {statistics['failed_count']} 个")

            # 提取测试结果
            test_results = self._extract_test_results()
            print(f"📋 提取到 {len(test_results)} 个测试用例结果")

            # 保存到数据库
            if self._save_test_results(test_results, device_info):
                print(f"✅ Allure报告数据转换并存储到数据库成功！执行ID: {self.execution_id}")
                return True
            else:
                print("❌ 数据保存失败")
                return False

        except Exception as e:
            print(f"❌ 转换过程中发生错误: {e}")
            return False

    def initialize_database(self) -> bool:
        """
        初始化数据库（创建数据库和表）

        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        print("开始初始化数据库...")

        try:
            # 创建数据库
            if not self.db_manager.create_database_if_not_exists():
                print("❌ 创建数据库失败")
                return False

            # 执行表结构SQL
            schema_file = Path(__file__).parent / "database_schema_v2.sql"
            if not self.db_manager.execute_sql_file(str(schema_file)):
                print("❌ 创建数据库表失败")
                return False

            print("✅ 数据库初始化成功")
            return True

        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            return False


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='将Allure报告转换并存储到MySQL数据库')
    parser.add_argument('--reports-dir', '-r', default=None,
                        help='报告目录路径 (默认: 自动检测项目根目录下的reports)')
    parser.add_argument('--init-db', action='store_true',
                        help='初始化数据库（创建数据库和表）')
    parser.add_argument('--db-host', default='localhost',
                        help='数据库主机地址 (默认: localhost)')
    parser.add_argument('--db-port', type=int, default=3306,
                        help='数据库端口 (默认: 3306)')
    parser.add_argument('--db-user', default='root',
                        help='数据库用户名 (默认: root)')
    parser.add_argument('--db-password', default='root123456',
                        help='数据库密码 (默认: root123456)')
    parser.add_argument('--db-name', default='ella_allure_report',
                        help='数据库名称 (默认: ella_allure_report)')

    args = parser.parse_args()

    # 创建数据库配置
    db_config = DatabaseConfig(
        host=args.db_host,
        port=args.db_port,
        username=args.db_user,
        password=args.db_password,
        database=args.db_name
    )

    # 创建转换器
    converter = AllureToMySQLConverter(args.reports_dir, db_config)

    print(f"📁 Allure结果目录: {converter.allure_results_dir}")
    print(f"🗄️ 数据库配置: {db_config.username}@{db_config.host}:{db_config.port}/{db_config.database}")

    try:
        # 如果指定了初始化数据库选项
        if args.init_db:
            if not converter.initialize_database():
                print("\n❌ 数据库初始化失败")
                return 1
            print("\n✅ 数据库初始化完成")

        # 执行转换
        if converter.convert_to_database():
            print(f"\n✅ 转换完成！数据已保存到数据库")
            print(f"执行批次ID: {converter.execution_id}")
        else:
            print(f"\n❌ 转换失败")
            return 1

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
