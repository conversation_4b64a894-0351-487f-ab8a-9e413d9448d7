#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel批量挂测结果自动分析工具
自动分析Excel中批量挂测结果，按照失败原因归类

作者: AI Assistant
创建时间: 2025-08-27
"""

import pandas as pd
import os
import re
from datetime import datetime
from typing import Dict, List, Tuple


class ExcelFailureAnalyzer:
    """Excel失败测试用例分析器"""
    
    def __init__(self, excel_path: str):
        """
        初始化分析器
        
        Args:
            excel_path: Excel文件路径
        """
        self.excel_path = excel_path
        self.df = None
        self.failed_records = []
        
        # 预期结果关键词
        self.support_keywords = ['Done', 'Multiple settings']
        self.not_support_keywords = ['Sorry', 'Oops', 'out of my reach','not supported']
        
        # 获取当前日期
        self.current_date = datetime.now().strftime('%Y%m%d')
    
    def load_excel(self) -> bool:
        """
        加载Excel文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(self.excel_path):
                print(f"错误：文件不存在 - {self.excel_path}")
                return False
            
            self.df = pd.read_excel(self.excel_path)
            print(f"成功加载Excel文件，共 {len(self.df)} 条记录")
            return True
            
        except Exception as e:
            print(f"加载Excel文件时出错: {e}")
            return False
    
    def extract_expected_and_actual(self, failure_message: str) -> Tuple[str, str]:
        """
        从失败消息中提取预期结果和实际结果

        Args:
            failure_message: 失败消息

        Returns:
            Tuple[str, str]: (预期结果, 实际结果)
        """
        try:
            # 使用正则表达式提取预期结果和实际结果
            expected_pattern = r"响应文本应包含\[(.*?)\]"
            # 修改实际结果的正则表达式，匹配到行尾或assert之前
            actual_pattern = r"实际响应:\s*'(.*?)'\s*(?:assert|$)"

            expected_match = re.search(expected_pattern, failure_message)
            actual_match = re.search(actual_pattern, failure_message, re.DOTALL)

            expected = expected_match.group(1) if expected_match else ""
            actual = actual_match.group(1) if actual_match else ""

            # 清理引号
            expected = expected.strip("'\"")

            return expected, actual

        except Exception as e:
            print(f"提取预期和实际结果时出错: {e}")
            return "", ""
    
    def classify_failure_reason(self, expected: str, actual: str, test_status: str) -> Dict[str, str]:
        """
        根据规则分类失败原因
        
        Args:
            expected: 预期结果
            actual: 实际结果
            test_status: 测试状态
            
        Returns:
            Dict[str, str]: 包含问题归类、失败原因、解决办法、修复日期的字典
        """
        result = {
            '问题归类': '',
            '失败原因': '',
            '解决办法': '',
            '修复日期': self.current_date
        }
        
        if test_status.lower() == 'failed':
            result['问题归类'] = '脚本问题'
            
            # 检查是否为指令支持变更
            expected_is_support = any(keyword in expected for keyword in self.support_keywords)
            expected_is_not_support = any(keyword in expected for keyword in self.not_support_keywords)
            
            actual_is_support = any(keyword in actual for keyword in self.support_keywords)
            actual_is_not_support = any(keyword in actual for keyword in self.not_support_keywords)
            
            if expected_is_support and actual_is_not_support:
                result['失败原因'] = '指令支持变更：支持->不支持'
                result['解决办法'] = '修复脚本'
            elif expected_is_not_support and actual_is_support:
                result['失败原因'] = '指令支持变更：不支持->支持'
                result['解决办法'] = '修复脚本'
            else:
                result['失败原因'] = '待人工排查'
                result['解决办法'] = ''
                
        elif test_status.lower() == 'error':
            result['问题归类'] = '环境问题'
            result['失败原因'] = '环境问题'
            result['解决办法'] = ''
        
        return result
    
    def analyze_failures(self) -> List[Dict]:
        """
        分析所有失败记录
        
        Returns:
            List[Dict]: 失败记录列表
        """
        if self.df is None:
            print("错误：请先加载Excel文件")
            return []
        
        # 筛选失败和错误的测试用例
        failed_df = self.df[self.df['测试状态'].isin(['failed', 'error'])]
        
        print(f"找到 {len(failed_df)} 个失败/错误的测试用例")
        
        failed_records = []
        
        for idx, row in failed_df.iterrows():
            # 提取基本信息
            record = {
                '标签_suite': row.get('标签_suite', ''),
                '标签_parentSuite': row.get('标签_parentSuite', ''),
                '测试用例名称': row.get('测试用例名称', ''),
                '测试状态': row.get('测试状态', ''),
                '失败消息': row.get('失败消息', ''),
                '失败原因_原始': row.get('失败原因', '')
            }
            
            # 提取预期结果和实际结果
            expected, actual = self.extract_expected_and_actual(str(row.get('失败消息', '')))
            
            # 分类失败原因
            classification = self.classify_failure_reason(expected, actual, row.get('测试状态', ''))
            
            # 合并信息
            record.update(classification)
            record['预期结果'] = expected
            record['实际结果'] = actual
            
            failed_records.append(record)
        
        self.failed_records = failed_records
        return failed_records
    
    def generate_failure_report(self, output_path: str = None) -> str:
        """
        生成失败记录报告Excel文件

        Args:
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            str: 输出文件路径
        """
        if not self.failed_records:
            print("错误：没有失败记录可生成报告")
            return ""

        # 生成输出文件路径
        if output_path is None:
            # 创建failure_analysis目录
            output_dir = "failure_analysis"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建目录: {output_dir}")

            base_name = os.path.splitext(os.path.basename(self.excel_path))[0]
            output_path = f"{output_dir}/failure_analysis_{base_name}.xlsx"
        
        try:
            # 创建DataFrame
            report_df = pd.DataFrame(self.failed_records)
            
            # 选择需要的列
            columns_to_export = [
                '标签_suite', '标签_parentSuite', '问题归类', '失败原因', '解决办法', '修复日期',
                '测试用例名称', '测试状态', '预期结果', '实际结果', '失败消息'
            ]
            
            # 确保所有列都存在
            for col in columns_to_export:
                if col not in report_df.columns:
                    report_df[col] = ''
            
            export_df = report_df[columns_to_export]
            
            # 保存到Excel
            export_df.to_excel(output_path, index=False, engine='openpyxl')
            
            print(f"失败分析报告已生成: {output_path}")
            print(f"共包含 {len(export_df)} 条失败记录")
            
            # 打印统计信息
            self.print_statistics()
            
            return output_path
            
        except Exception as e:
            print(f"生成报告时出错: {e}")
            return ""
    
    def print_statistics(self):
        """打印统计信息"""
        if not self.failed_records:
            return
        
        df = pd.DataFrame(self.failed_records)
        
        print("\n=== 失败原因统计 ===")
        problem_stats = df['问题归类'].value_counts()
        for category, count in problem_stats.items():
            print(f"{category}: {count} 个")
        
        print("\n=== 具体失败原因统计 ===")
        reason_stats = df['失败原因'].value_counts()
        for reason, count in reason_stats.items():
            print(f"{reason}: {count} 个")


def main():
    """主函数"""
    import sys

    # 支持命令行参数
    if len(sys.argv) > 1:
        excel_path = sys.argv[1]
    else:
        # 默认Excel文件路径
        excel_path = r"D:\app_test\tools\allure_report_analysis\allure_report_excel\allure_report_25-09-02_18-03-19.xlsx"

    print(f"开始分析Excel文件: {excel_path}")

    # 创建分析器
    analyzer = ExcelFailureAnalyzer(excel_path)

    # 加载Excel文件
    if not analyzer.load_excel():
        return

    # 分析失败记录
    failed_records = analyzer.analyze_failures()

    if failed_records:
        # 生成报告
        output_path = analyzer.generate_failure_report()

        if output_path:
            print(f"\n✅ 分析完成！")
            print(f"📊 报告文件: {output_path}")
            print(f"📈 共分析了 {len(failed_records)} 个失败测试用例")
        else:
            print("\n❌ 生成报告失败")
    else:
        print("✅ 没有找到失败的测试用例，所有测试都通过了！")


if __name__ == "__main__":
    # main()
    result_analyzer = ExcelFailureAnalyzer(r"D:\app_test\tools\allure_report_analysis\allure_report_excel\ASALE3741B000022_report_25-09-05_14-35-16.xlsx")
    result_analyzer.load_excel()
    result_analyzer.analyze_failures()
    result_analyzer.generate_failure_report()
