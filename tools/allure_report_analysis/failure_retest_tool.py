#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败用例重测工具
读取失败分析Excel文件，过滤指定失败原因的用例，组织成pytest套件进行重测

作者: AI Assistant
创建时间: 2025-09-03
"""

import os
import sys
import pandas as pd
import subprocess
import tempfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set
from core.logger import log
from utils.file_utils import FileUtils


class FailureRetestTool:
    """失败用例重测工具"""
    
    def __init__(self, excel_path: str = None):
        """
        初始化重测工具
        
        Args:
            excel_path: Excel文件路径，如果为None则自动查找最新的
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.excel_path = excel_path or self._find_latest_failure_analysis_excel()
        self.filtered_cases = []
        self.target_failure_reasons = [
            '指令支持变更：支持->不支持',
            '指令支持变更：不支持->支持'
        ]
        
        # 创建临时目录用于存放重测脚本
        self.temp_dir = self.project_root / "temp"
        self.temp_dir.mkdir(exist_ok=True)
        
        log.info(f"失败用例重测工具初始化完成")
        log.info(f"Excel文件路径: {self.excel_path}")
        log.info(f"临时目录: {self.temp_dir}")
    
    def _find_latest_failure_analysis_excel(self) -> str:
        """
        查找最新的失败分析Excel文件
        根据文件名中的时间戳找到离当前时间最近的Excel文件

        文件名格式: failure_analysis_allure_report_25-09-03_13-26-24.xlsx
        时间戳格式: YY-MM-DD_HH-MM-SS

        Returns:
            str: 最新Excel文件路径
        """
        failure_analysis_dir = self.project_root / "tools" / "allure_report_analysis" / "failure_analysis"

        if not failure_analysis_dir.exists():
            raise FileNotFoundError(f"失败分析目录不存在: {failure_analysis_dir}")

        # 查找所有failure_analysis_*.xlsx文件
        excel_files = list(failure_analysis_dir.glob("failure_analysis_*.xlsx"))

        if not excel_files:
            raise FileNotFoundError(f"未找到失败分析Excel文件: {failure_analysis_dir}")

        # 按文件名中的时间戳排序，返回最新的
        latest_file = max(excel_files, key=self._extract_timestamp_from_filename)
        return str(latest_file)

    def _extract_timestamp_from_filename(self, file_path: Path) -> datetime:
        """
        从文件名中提取时间戳

        Args:
            file_path: 文件路径

        Returns:
            datetime: 提取的时间戳，如果提取失败返回最小时间
        """
        import re
        from datetime import datetime

        try:
            # 文件名格式: failure_analysis_allure_report_25-09-03_13-26-24.xlsx
            # 提取时间戳部分: 25-09-03_13-26-24
            filename = file_path.name

            # 使用正则表达式提取时间戳
            # 匹配格式: YY-MM-DD_HH-MM-SS
            pattern = r'(\d{2})-(\d{2})-(\d{2})_(\d{2})-(\d{2})-(\d{2})'
            match = re.search(pattern, filename)

            if match:
                year, month, day, hour, minute, second = match.groups()

                # 将2位年份转换为4位年份（假设20xx年）
                full_year = 2000 + int(year)

                # 创建datetime对象
                timestamp = datetime(
                    year=full_year,
                    month=int(month),
                    day=int(day),
                    hour=int(hour),
                    minute=int(minute),
                    second=int(second)
                )

                log.debug(f"从文件名 {filename} 提取时间戳: {timestamp}")
                return timestamp
            else:
                log.warning(f"无法从文件名 {filename} 提取时间戳，使用文件修改时间")
                # 如果无法从文件名提取时间戳，使用文件修改时间
                return datetime.fromtimestamp(file_path.stat().st_mtime)

        except Exception as e:
            log.warning(f"提取时间戳时出错 {file_path.name}: {e}，使用最小时间")
            # 如果出错，返回最小时间，这样该文件会被排在最后
            return datetime.min
    
    def load_and_filter_excel(self) -> bool:
        """
        加载Excel文件并过滤指定失败原因的用例
        
        Returns:
            bool: 是否成功加载和过滤
        """
        try:
            if not os.path.exists(self.excel_path):
                log.error(f"Excel文件不存在: {self.excel_path}")
                return False
            
            # 读取Excel文件
            df = pd.read_excel(self.excel_path)
            log.info(f"成功加载Excel文件，共 {len(df)} 条记录")
            
            # 过滤指定失败原因的记录
            filtered_df = df[df['失败原因'].isin(self.target_failure_reasons)]
            log.info(f"过滤后找到 {len(filtered_df)} 条符合条件的失败用例")
            
            # 转换为字典列表
            self.filtered_cases = filtered_df.to_dict('records')
            
            # 打印统计信息
            self._print_filter_statistics()
            
            return True
            
        except Exception as e:
            log.error(f"加载和过滤Excel文件时出错: {e}")
            return False
    
    def _print_filter_statistics(self):
        """打印过滤统计信息"""
        if not self.filtered_cases:
            log.warning("没有找到符合条件的失败用例")
            return
        
        log.info("\n=== 过滤结果统计 ===")
        
        # 按失败原因统计
        reason_count = {}
        suite_count = {}
        
        for case in self.filtered_cases:
            reason = case.get('失败原因', '未知')
            suite = case.get('标签_suite', '未知')
            
            reason_count[reason] = reason_count.get(reason, 0) + 1
            suite_count[suite] = suite_count.get(suite, 0) + 1
        
        log.info("按失败原因统计:")
        for reason, count in reason_count.items():
            log.info(f"  {reason}: {count} 个")
        
        log.info("按测试套件统计:")
        for suite, count in suite_count.items():
            log.info(f"  {suite}: {count} 个")
    
    def generate_test_cases_mapping(self) -> Dict[str, Set[str]]:
        """
        生成测试用例映射关系
        只包含真实存在的测试文件

        Returns:
            Dict[str, Set[str]]: 套件名到测试用例文件的映射
        """
        test_mapping = {}
        valid_files_count = 0
        invalid_files_count = 0

        for case in self.filtered_cases:
            suite = case.get('标签_suite', '')
            parent_suite = case.get('标签_parentSuite', '')
            test_name = case.get('测试用例名称', '')

            if not suite or not test_name:
                continue

            # 根据测试用例名称推断文件路径
            test_file = self._infer_test_file_path(suite, test_name)

            if test_file:
                # 验证文件是否真实存在
                full_path = self.project_root / test_file
                if full_path.exists():
                    # 使用parent_suite作为分组键，更好地组织测试文件
                    group_key = parent_suite if parent_suite else suite
                    if group_key not in test_mapping:
                        test_mapping[group_key] = set()
                    test_mapping[group_key].add(test_file)
                    valid_files_count += 1
                else:
                    log.warning(f"测试文件不存在，跳过: {test_file}")
                    invalid_files_count += 1
            else:
                invalid_files_count += 1

        log.info(f"文件验证结果: 有效文件 {valid_files_count} 个，无效文件 {invalid_files_count} 个")
        return test_mapping
    
    def _infer_test_file_path(self, suite: str, test_name: str) -> str:
        """
        根据套件和测试名称推断测试文件路径

        规则：
        1. 标签_parentSuite字段是脚本目录，例如：testcases.test_ella.system_coupling -> testcases/test_ella/system_coupling
        2. 标签_suite字段是脚本名称，例如：test_turn_off_adaptive_brightness
        3. 脚本完整路径：testcases/test_ella/system_coupling/test_turn_off_adaptive_brightness.py

        Args:
            suite: 测试套件名称（标签_suite字段）
            test_name: 测试用例名称

        Returns:
            str: 测试文件路径，如果找不到返回空字符串
        """
        # 确保suite以.py结尾
        if not suite.endswith('.py'):
            suite += '.py'

        # 从filtered_cases中查找对应的parentSuite信息
        parent_suite = ""
        for case in self.filtered_cases:
            if case.get('标签_suite', '') == suite.replace('.py', ''):
                parent_suite = case.get('标签_parentSuite', '')
                break

        if parent_suite:
            # 将点号分隔的包路径转换为文件系统路径
            # 例如：testcases.test_ella.system_coupling -> testcases/test_ella/system_coupling
            directory_path = parent_suite.replace('.', '/')
            test_file_path = f"{directory_path}/{suite}"
        else:
            # 如果没有找到parentSuite，使用原有的推断逻辑作为备选
            file_name = suite

            # 构建可能的文件路径
            possible_paths = [
                f"testcases/test_ella/{file_name}",
                f"testcases/test_ella/component_coupling/{file_name}",
                f"testcases/test_ella/dialogue/{file_name}",
                f"testcases/test_ella/self_function/{file_name}",
                f"testcases/test_ella/system_coupling/{file_name}",
                f"testcases/test_ella/third_coupling/{file_name}",
                f"testcases/test_ella/unsupported_commands/{file_name}",
                f"testcases/test_ella/test_ask_screen/{file_name}",
            ]

            # 检查文件是否存在
            for path in possible_paths:
                full_path = self.project_root / path
                if full_path.exists():
                    return path

            log.warning(f"未找到测试文件: {file_name} (suite: {suite})")
            return ""

        # 检查推断的路径是否存在
        full_path = self.project_root / test_file_path
        if full_path.exists():
            log.info(f"找到测试文件: {test_file_path}")
            return test_file_path
        else:
            log.warning(f"推断的测试文件不存在: {test_file_path}")
            return ""
    
    def create_retest_script(self) -> str:
        """
        创建重测脚本
        只包含真实存在的测试文件

        Returns:
            str: 重测脚本路径
        """
        test_mapping = self.generate_test_cases_mapping()

        if not test_mapping:
            log.error("没有找到有效的测试用例文件")
            return ""

        # 统计有效测试文件数量
        total_files = sum(len(files) for files in test_mapping.values())
        log.info(f"准备生成包含 {total_files} 个有效测试文件的重测脚本")

        # 生成脚本内容
        script_content = self._generate_retest_script_content(test_mapping)

        # 保存脚本文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        script_path = self.temp_dir / f"retest_failures_{timestamp}.py"

        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            log.info(f"重测脚本已生成: {script_path}")
            log.info(f"包含 {total_files} 个有效测试文件")
            return str(script_path)

        except Exception as e:
            log.error(f"生成重测脚本时出错: {e}")
            return ""
    
    def _generate_retest_script_content(self, test_mapping: Dict[str, Set[str]]) -> str:
        """
        生成重测脚本内容
        
        Args:
            test_mapping: 测试用例映射
            
        Returns:
            str: 脚本内容
        """
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失败用例重测脚本
自动生成时间: {timestamp}
重测目标: 指令支持变更相关的失败用例

本脚本基于失败分析Excel文件自动生成，用于重新测试以下失败原因的用例：
- 指令支持变更：支持->不支持
- 指令支持变更：不支持->支持
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from run_tests import TestRunner
from core.logger import log


class FailureRetestRunner:
    """失败用例重测执行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_runner = TestRunner()
        
        # 需要重测的测试文件列表
        self.test_files = [
'''
        
        # 添加测试文件列表
        all_test_files = set()
        for suite, files in test_mapping.items():
            all_test_files.update(files)
        
        for test_file in sorted(all_test_files):
            script_content += f'            "{test_file}",\n'
        
        script_content += '''        ]
    
    def run_retest(self):
        """执行重测"""
        log.info("开始执行失败用例重测")
        log.info(f"共需要重测 {len(self.test_files)} 个测试文件")
        
        # 构建测试路径字符串
        test_paths = " ".join(self.test_files)
        
        # 执行重测
        exit_code = self.test_runner.run_tests(
            test_path=test_paths,
            markers="",
            verbose=True,
            generate_report=True,
            open_report=False
        )
        
        if exit_code == 0:
            log.info("✅ 重测完成，所有测试通过")
        else:
            log.warning(f"⚠️ 重测完成，存在失败用例，退出码: {exit_code}")
        
        return exit_code


def main():
    """主函数"""
    log.info("=== 失败用例重测工具 ===")
    
    runner = FailureRetestRunner()
    exit_code = runner.run_retest()
    
    log.info("=== 重测完成 ===")
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
'''
        
        return script_content


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="失败用例重测工具")
    parser.add_argument(
        "--excel-path",
        help="失败分析Excel文件路径，不指定则自动查找最新的"
    )
    parser.add_argument(
        "--run-immediately",
        action="store_true",
        help="生成脚本后立即执行重测"
    )
    
    args = parser.parse_args()
    
    try:
        # 创建重测工具
        tool = FailureRetestTool(args.excel_path)
        
        # 加载和过滤Excel数据
        if not tool.load_and_filter_excel():
            log.error("加载Excel文件失败")
            return 1
        
        # 生成重测脚本
        script_path = tool.create_retest_script()
        if not script_path:
            log.error("生成重测脚本失败")
            return 1
        
        log.info(f"✅ 重测脚本生成成功: {script_path}")
        
        # 如果指定立即执行，则运行脚本
        if args.run_immediately:
            log.info("开始执行重测...")
            result = subprocess.run([sys.executable, script_path], cwd=tool.project_root)
            return result.returncode
        else:
            log.info(f"请手动执行重测脚本: python {script_path}")
            return 0
            
    except Exception as e:
        log.error(f"执行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
