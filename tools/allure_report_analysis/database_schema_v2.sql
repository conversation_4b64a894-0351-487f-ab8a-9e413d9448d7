-- Ella Allure测试报告数据库表结构 V3 (优化版)
-- 创建时间: 2025-09-04
-- 更新内容: 简化表结构，仅保留核心测试结果表

-- 删除废弃的表
DROP TABLE IF EXISTS `test_containers`;
DROP TABLE IF EXISTS `test_case_labels`;
DROP TABLE IF EXISTS `test_case_results`;
DROP TABLE IF EXISTS `test_executions`;
DROP TABLE IF EXISTS `test_containers_v2`;
DROP TABLE IF EXISTS `test_case_labels_v2`;
DROP TABLE IF EXISTS `test_case_results_v2`;
DROP TABLE IF EXISTS `test_executions_v2`;
DROP TABLE IF EXISTS `devices`;

-- 核心测试结果表（合并所有重要信息）
CREATE TABLE IF NOT EXISTS `test_results` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `execution_id` VARCHAR(64) NOT NULL COMMENT '执行批次ID（UUID）',
    `test_case_uuid` VARCHAR(64) NOT NULL COMMENT '用例UUID',
    `test_case_name` VARCHAR(500) NOT NULL COMMENT '测试用例名称',
    `test_status` VARCHAR(20) NOT NULL COMMENT '测试状态（passed/failed/broken/skipped/unknown）',
    `command_type` ENUM('text', 'voice') DEFAULT 'text' COMMENT '指令类型（文本/语音）',

    -- 设备信息
    `device_id` VARCHAR(100) NOT NULL COMMENT '设备ID',
    `device_name` VARCHAR(200) DEFAULT NULL COMMENT '设备名称',
    `device_brand` VARCHAR(100) DEFAULT NULL COMMENT '设备品牌',
    `device_model` VARCHAR(100) DEFAULT NULL COMMENT '设备型号',
    `android_version` VARCHAR(50) DEFAULT NULL COMMENT 'Android版本',
    `platform_version` VARCHAR(50) DEFAULT NULL COMMENT '平台版本',

    -- 执行信息
    `execution_date` DATE NOT NULL COMMENT '执行日期',
    `execution_time` DATETIME NOT NULL COMMENT '执行时间',
    `start_time` DATETIME DEFAULT NULL COMMENT '用例开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '用例结束时间',
    `duration_ms` BIGINT DEFAULT NULL COMMENT '用例执行时长（毫秒）',

    -- 测试详情
    `description` TEXT DEFAULT NULL COMMENT '用例描述',
    `full_name` VARCHAR(1000) DEFAULT NULL COMMENT '完整名称',
    `history_id` VARCHAR(100) DEFAULT NULL COMMENT '历史ID',
    `test_case_id` VARCHAR(100) DEFAULT NULL COMMENT '测试用例ID',
    `failure_message` TEXT DEFAULT NULL COMMENT '失败消息',
    `failure_trace` LONGTEXT DEFAULT NULL COMMENT '失败堆栈',
    `failure_reason` TEXT DEFAULT NULL COMMENT '失败原因简述',

    -- 步骤和附件信息
    `steps_count` INT DEFAULT 0 COMMENT '步骤数量',
    `steps_details` LONGTEXT DEFAULT NULL COMMENT '步骤详情',
    `attachments_count` INT DEFAULT 0 COMMENT '附件数量',
    `attachments_details` TEXT DEFAULT NULL COMMENT '附件详情',
    `parameters_count` INT DEFAULT 0 COMMENT '参数数量',
    `parameters_details` TEXT DEFAULT NULL COMMENT '参数详情',

    -- 标签信息（完整标签）
    `parent_suite` VARCHAR(200) DEFAULT NULL COMMENT 'parentSuite标签值',
    `suite` VARCHAR(200) DEFAULT NULL COMMENT 'suite标签值',
    `sub_suite` VARCHAR(200) DEFAULT NULL COMMENT 'subSuite标签值',
    `feature` VARCHAR(200) DEFAULT NULL COMMENT 'feature标签值',
    `story` VARCHAR(200) DEFAULT NULL COMMENT 'story标签值',
    `severity` VARCHAR(50) DEFAULT NULL COMMENT 'severity标签值',
    `tag` VARCHAR(200) DEFAULT NULL COMMENT 'tag标签值',
    `host` VARCHAR(100) DEFAULT NULL COMMENT 'host标签值',
    `thread` VARCHAR(100) DEFAULT NULL COMMENT 'thread标签值',
    `framework` VARCHAR(50) DEFAULT NULL COMMENT 'framework标签值',
    `language` VARCHAR(50) DEFAULT NULL COMMENT 'language标签值',
    `package` VARCHAR(500) DEFAULT NULL COMMENT 'package标签值',
    `epic` VARCHAR(200) DEFAULT NULL COMMENT 'epic标签值',

    -- 元数据
    `data_source` ENUM('allure_json', 'excel_import') DEFAULT 'excel_import' COMMENT '数据来源',
    `excel_file_name` VARCHAR(500) DEFAULT NULL COMMENT 'Excel文件名（Excel导入时）',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    -- 索引
    INDEX `idx_execution_id` (`execution_id`),
    INDEX `idx_test_case_uuid` (`test_case_uuid`),
    INDEX `idx_test_status` (`test_status`),
    INDEX `idx_command_type` (`command_type`),
    INDEX `idx_device_id` (`device_id`),
    INDEX `idx_execution_date` (`execution_date`),
    INDEX `idx_execution_time` (`execution_time`),
    INDEX `idx_parent_suite` (`parent_suite`),
    INDEX `idx_suite` (`suite`),
    INDEX `idx_severity` (`severity`),
    INDEX `idx_data_source` (`data_source`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_test_case_name` (`test_case_name`(255)),

    -- 复合索引
    INDEX `idx_device_date` (`device_id`, `execution_date`),
    INDEX `idx_status_type` (`test_status`, `command_type`),
    INDEX `idx_execution_device` (`execution_id`, `device_id`),
    INDEX `idx_suite_start_time` (`suite`, `start_time`, `device_id`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='核心测试结果表';
