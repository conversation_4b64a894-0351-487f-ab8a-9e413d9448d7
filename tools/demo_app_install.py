#!/usr/bin/env python3
"""
应用安装功能演示脚本
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from tools.adb_process_monitor import AdbProcessMonitor

def demo_package_name_mapping():
    """演示应用名称到包名的映射功能"""
    print("🔍 应用名称到包名映射演示")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    test_apps = [
        "WhatsApp", "Chrome", "WeChat", "YouTube", "Gmail",
        "Facebook", "Instagram", "Twitter", "Netflix", "Spotify"
    ]
    
    for app_name in test_apps:
        package_name = monitor._get_package_name_by_app_name(app_name)
        if package_name:
            print(f"✅ {app_name:<12} → {package_name}")
        else:
            print(f"❌ {app_name:<12} → 未找到包名")

def demo_installation_check():
    """演示安装状态检查功能"""
    print("\n📦 应用安装状态检查演示")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    test_packages = [
        ("Chrome", "com.android.chrome"),
        ("Gmail", "com.google.android.gm"),
        ("Settings", "com.android.settings"),
        ("Calculator", "com.android.calculator2"),
        ("WhatsApp", "com.whatsapp")
    ]
    
    for app_name, package_name in test_packages:
        is_installed = monitor.is_package_installed(package_name)
        status = "已安装" if is_installed else "未安装"
        icon = "✅" if is_installed else "❌"
        print(f"{icon} {app_name:<12} ({package_name}) → {status}")

def demo_apk_search_patterns():
    """演示APK搜索模式生成"""
    print("\n🔍 APK搜索模式生成演示")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    test_cases = [
        ("WhatsApp", "com.whatsapp"),
        ("Google Chrome", "com.android.chrome"),
        ("WeChat", "com.tencent.mm")
    ]
    
    for app_name, package_name in test_cases:
        patterns = monitor._generate_apk_search_patterns(app_name, package_name)
        print(f"\n📱 {app_name} ({package_name}):")
        for i, pattern in enumerate(patterns[:5], 1):  # 只显示前5个模式
            print(f"   {i}. {pattern}")

def demo_full_install_process():
    """演示完整的安装流程（不实际安装）"""
    print("\n🚀 完整安装流程演示")
    print("=" * 50)
    
    monitor = AdbProcessMonitor()
    
    # 选择一个常见的应用进行演示
    app_name = "Chrome"
    apk_dir = "/data"
    
    print(f"📱 应用名称: {app_name}")
    print(f"📁 搜索目录: {apk_dir}")
    print()
    
    # 步骤1: 获取包名
    package_name = monitor._get_package_name_by_app_name(app_name)
    if package_name:
        print(f"✅ 步骤1 - 包名映射: {app_name} → {package_name}")
    else:
        print(f"❌ 步骤1 - 包名映射失败")
        return
    
    # 步骤2: 检查安装状态
    is_installed = monitor.is_package_installed(package_name)
    if is_installed:
        print(f"✅ 步骤2 - 应用已安装: {package_name}")
        return
    else:
        print(f"📦 步骤2 - 应用未安装，需要安装")
    
    # 步骤3: 生成搜索模式
    patterns = monitor._generate_apk_search_patterns(app_name, package_name)
    print(f"🔍 步骤3 - 生成 {len(patterns)} 个搜索模式")
    
    # 步骤4: 模拟搜索（不实际搜索文件系统）
    print(f"📁 步骤4 - 在 {apk_dir} 中搜索APK文件...")
    print(f"   搜索模式示例: {patterns[0] if patterns else 'N/A'}")
    
    print(f"⚠️  注意: 这只是演示，未实际搜索或安装文件")

def main():
    """主函数"""
    print("🎯 ADB进程监控工具 - 应用安装功能演示")
    print("=" * 60)
    
    try:
        demo_package_name_mapping()
        demo_installation_check()
        demo_apk_search_patterns()
        demo_full_install_process()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成!")
        print("\n💡 使用提示:")
        print("   python adb_process_monitor.py --install-app WhatsApp")
        print("   python adb_process_monitor.py --install-app Chrome --apk-dir /sdcard")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")

if __name__ == '__main__':
    main()
