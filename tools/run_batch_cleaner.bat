@echo off
chcp 65001 >nul
echo ========================================
echo 批量目录清理工具
echo ========================================
echo.

cd /d "%~dp0\.."

echo 请选择清理模式:
echo 1. 清理常用目录 (相机、截图、录屏、下载)
echo 2. 清理媒体目录 (相机、图片、视频、音乐)
echo 3. 清理测试目录 (下载、相机)
echo 4. 自定义配置文件
echo 5. 生成示例配置文件
echo 6. 查看预设配置
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" (
    echo 正在清理常用目录...
    python tools/batch_directory_cleaner.py --preset common
) else if "%choice%"=="2" (
    echo 正在清理媒体目录...
    python tools/batch_directory_cleaner.py --preset media
) else if "%choice%"=="3" (
    echo 正在清理测试目录...
    python tools/batch_directory_cleaner.py --preset test
) else if "%choice%"=="4" (
    set /p config_file="请输入配置文件路径: "
    echo 使用配置文件清理...
    python tools/batch_directory_cleaner.py --config "%config_file%"
) else if "%choice%"=="5" (
    echo 生成示例配置文件...
    python tools/batch_directory_cleaner.py --generate-config
) else if "%choice%"=="6" (
    echo 查看预设配置...
    python tools/batch_directory_cleaner.py --list-presets
) else (
    echo 无效选择，使用默认配置清理常用目录...
    python tools/batch_directory_cleaner.py --preset common
)

echo.
echo 按任意键退出...
pause >nul
