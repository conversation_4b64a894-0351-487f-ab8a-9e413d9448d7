@echo off
chcp 65001 >nul
echo ========================================
echo Excel批量挂测结果自动分析工具
echo ========================================
echo.

cd /d "%~dp0\.."

if "%1"=="" (
    echo 使用默认Excel文件路径...
    python tools/allure_report_analysis/excel_failure_analyzer.py
) else (
    echo 使用指定Excel文件: %1
    python tools/allure_report_analysis/excel_failure_analyzer.py "%1"
)

echo.
echo 按任意键退出...
pause >nul
