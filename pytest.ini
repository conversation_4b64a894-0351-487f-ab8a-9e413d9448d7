[pytest]
testpaths = testcases
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --alluredir=reports/allure-results
    --clean-alluredir
    -v
    -s
    --tb=short
markers =
    smoke: 冒烟测试
    regression: 回归测试
    critical: 关键功能测试
    calculator: 计算器相关测试
    settings: 设置相关测试
    screen_management: 标记需要特殊屏幕管理的测试
    long_running: 标记长时间运行的测试
    quick_test: 标记快速测试
    no_recording: 标记不需要录屏的测试
    high_quality_recording: 标记需要高质量录屏的测试
