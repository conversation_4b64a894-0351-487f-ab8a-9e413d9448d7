"""
通用页面基类
包含所有应用通用的页面元素和操作
"""
from core.base_page import BasePage
from core.base_element import BaseElement
from core.logger import log


class CommonPage(BasePage):
    """通用页面基类"""
    
    def __init__(self, app_name: str = "", page_name: str = "", driver=None):
        """
        初始化通用页面

        Args:
            app_name: 应用名称
            page_name: 页面名称
            driver: 设备驱动实例
        """
        super().__init__(app_name, page_name, driver=driver)
        
        # 通用元素定位器
        self._init_common_elements()
    
    def _init_common_elements(self):
        """初始化通用元素"""
        # 常见的确定按钮
        self.btn_ok = self.create_element(
            {"text": "确定"}, 
            "确定按钮"
        )
        
        self.btn_ok_en = self.create_element(
            {"text": "OK"}, 
            "OK按钮"
        )
        
        # 常见的取消按钮
        self.btn_cancel = self.create_element(
            {"text": "取消"}, 
            "取消按钮"
        )
        
        self.btn_cancel_en = self.create_element(
            {"text": "Cancel"}, 
            "Cancel按钮"
        )
        
        # 常见的返回按钮
        self.btn_back = self.create_element(
            {"description": "Navigate up"}, 
            "返回按钮"
        )
        
        # 常见的菜单按钮
        self.btn_menu = self.create_element(
            {"description": "More options"}, 
            "菜单按钮"
        )
        
        # 常见的搜索框
        self.input_search = self.create_element(
            {"className": "android.widget.EditText", "text": "搜索"}, 
            "搜索框"
        )
        
        # 权限相关按钮
        self.btn_allow = self.create_element(
            {"text": "允许"}, 
            "允许按钮"
        )
        
        self.btn_allow_en = self.create_element(
            {"text": "Allow"}, 
            "Allow按钮"
        )
        
        self.btn_deny = self.create_element(
            {"text": "拒绝"}, 
            "拒绝按钮"
        )
        
        self.btn_deny_en = self.create_element(
            {"text": "Deny"}, 
            "Deny按钮"
        )
    
    def handle_permission_dialog(self, allow: bool = True) -> bool:
        """
        处理权限对话框
        
        Args:
            allow: 是否允许权限
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if allow:
                # 尝试点击允许按钮
                if self.btn_allow.is_exists():
                    return self.btn_allow.click()
                elif self.btn_allow_en.is_exists():
                    return self.btn_allow_en.click()
            else:
                # 尝试点击拒绝按钮
                if self.btn_deny.is_exists():
                    return self.btn_deny.click()
                elif self.btn_deny_en.is_exists():
                    return self.btn_deny_en.click()
            
            return False
            
        except Exception as e:
            log.error(f"处理权限对话框失败: {e}")
            return False
    
    def close_dialog(self) -> bool:
        """
        关闭对话框
        
        Returns:
            bool: 关闭是否成功
        """
        try:
            # 尝试点击确定按钮
            if self.btn_ok.is_exists():
                return self.btn_ok.click()
            elif self.btn_ok_en.is_exists():
                return self.btn_ok_en.click()
            # 尝试点击取消按钮
            elif self.btn_cancel.is_exists():
                return self.btn_cancel.click()
            elif self.btn_cancel_en.is_exists():
                return self.btn_cancel_en.click()
            
            return False
            
        except Exception as e:
            log.error(f"关闭对话框失败: {e}")
            return False
    
    def go_back(self) -> bool:
        """
        返回上一页
        
        Returns:
            bool: 返回是否成功
        """
        try:
            # 尝试点击返回按钮
            if self.btn_back.is_exists():
                return self.btn_back.click()
            else:
                # 使用系统返回键
                self.press_back()
                return True
                
        except Exception as e:
            log.error(f"返回上一页失败: {e}")
            return False
