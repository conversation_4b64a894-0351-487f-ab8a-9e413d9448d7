"""
Weather Detector检测器
"""
from typing import List, Dict, Any, Tuple, Optional
from pages.base.app_detector import BaseAppDetector, AppType


class WeatherDetector(BaseAppDetector):
    """天气应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.WEATHER)
    
    def get_package_names(self) -> List[str]:
        """获取天气应用包名列表"""
        return [
            "com.miui.weather",
            "com.android.weather",
            "com.google.android.apps.weather",
            "com.transsion.weather",
            "com.weather.forecast",
            "com.accuweather",
            "com.weather.channel",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取天气应用关键词列表"""
        return ["weather", "clima", "meteo", "天气"]
    
    def validate_weather_data(self, weather_data: Dict[str, Any]) -> Tu<PERSON>[bool, Optional[str]]:
        """
        验证天气数据是否在合理范围内
        
        Args:
            weather_data: 天气数据字典，当前验证温度信息
            
        Returns:
            Tuple[bool, Optional[str]]: (验证是否通过, 错误信息)
        """
        # 天气数据验证范围优化
        if not weather_data:
            return False, "天气数据为空"
            
        # 验证温度范围 (-10°C 到 +50°C)
        if 'temperature' in weather_data:
            temp = weather_data['temperature']
            if isinstance(temp, (int, float)):
                if temp < -10 or temp > 50:
                    return False, f"温度超出合理范围: {temp}°C"
            elif isinstance(temp, str):
                try:
                    temp_value = float(temp.replace('°C', '').replace('℃', '').strip())
                    if temp_value < -10 or temp_value > 50:
                        return False, f"温度超出合理范围: {temp}"
                except ValueError:
                    return False, f"无法解析温度值: {temp}"

        
        return True, None
