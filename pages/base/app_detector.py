"""
应用检测器 - 重构版本
使用策略模式和工厂模式优化应用检测功能
"""
import subprocess
import sys
import os
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union
from enum import Enum


# 添加项目根目录到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from core.logger import log
from tools.adb_process_monitor import AdbProcessMonitor


class AppType(Enum):
    """应用类型枚举"""
    # 原有的应用类型
    WEATHER = "weather"
    CAMERA = "camera"
    SETTINGS = "settings"
    CONTACTS = "contacts"
    FACEBOOK = "facebook"
    MUSIC = "music"
    CLOCK = "clock"
    MAPS = "maps"
    PLAYSTORE = "playstore"

    # Excel表格中的Component - 按功能分类
    # 问卷和驾驶模式
    QUESTIONNAIRE = "questionnaire"
    DRIVING_MODE = "driving_mode"

    # 通信类应用
    DIALER = "dialer"
    SMART_MESSAGE = "smart_message"
    GOOGLE_CONTACT = "google_contact"
    GOOGLE_PHONE = "google_phone"
    GOOGLE_MESSAGE = "google_message"

    # 系统和身份识别
    TRANS_ID = "trans_id"
    TPMS = "tpms"
    FINDER = "finder"
    SMART_TOUCH = "smart_touch"
    TRANSSION_TIPS = "transsion_tips"

    # 工具类应用
    RECORDER = "recorder"
    PHONE_MASTER = "phone_master"
    NOTES = "notes"
    SMART_SWITCH = "smart_switch"
    FILE_MANAGER = "file_manager"
    SMART_SCANNER = "smart_scanner"
    CARLCARE = "Carlcare"


    # AI和智能助手
    ELLA = "ella"
    MOL = "mol"
    AI_CORE_MODEL_MANAGER = "ai_core_model_manager"
    ELLA_SCREEN_RECOGNITION = "ella_screen_recognition"
    MICRO_INTELLIGENCE = "micro_intelligence"

    # 演示和展示
    DEMO_MODE = "demo_mode"

    # 娱乐和媒体
    AI_GALLERY = "ai_gallery"
    CLIPPER = "clipper"
    VISHAPLAYER = "vishaplayer"
    FLIPMUSIC = "flipmusic"
    CALENDAR = "calendar"
    FM_RADIO = "fm_radio"
    YOUTUBE = "youtube"
    HEALTHLIFE = "healthlife"
    WHATSAPP = "whatsapp"

    # 系统控制和个性化
    COMMON_CONTROL = "common_control"
    PERSONALIZATION = "personalization"
    SHAREVIA = "sharevia"
    ONE_HANDED_MODE = "one_handed_mode"
    SPLIT_SCREEN = "split_screen"
    GOOGLE_PIP = "google_pip"
    THUNDERBACK = "thunderback"
    FOLDING_SCREEN_ZONE = "folding_screen_zone"
    SMART_PANEL = "smart_panel"
    SMART_HUB = "smart_hub"
    TRANSSION_STYLUS = "transsion_stylus"
    ONE_TAP_BUTTON = "one_tap_button"

    # 系统设置和默认应用
    GOOGLE_DEFAULT = "google_default"
    FW_DUAL_SYSTEM = "fw_dual_system"
    MULTI_USER = "multi_user"
    CHILDMODE = "childmode"
    PRIVACY = "privacy"
    SALES_STATISTICS = "sales_statistics"
    HEADSET_CONTROL = "headset_control"
    LIGHT_EFFECTS = "light_effects"
    REMOVABLE_SPECIAL = "removable_special"
    RECENT = "recent"

    # 界面和主题
    WALLPAPER = "wallpaper"
    MAGAZINE_SERVICE = "magazine_service"
    LAUNCHER = "launcher"
    THEME = "theme"
    GLOBAL_SEARCH = "global_search"
    ZERO_SCREEN = "zero_screen"
    SMART_ASSISTANT = "smart_assistant"
    CUTE_PET = "cute_pet"
    GLOBAL_THEME = "global_theme"

    # 系统UI和扩展
    SYSTEM_UI = "system_ui"
    OS_SETTINGS_EXT = "os_settings_ext"
    SCREEN_RECORD = "screen_record"
    SCREEN_SHOT = "screen_shot"
    DYNAMIC_BAR = "dynamic_bar"
    FLIP_EXTERNAL_SCREEN = "flip_external_screen"
    AOD = "aod"
    OOBE = "oobe"
    LEATHERCASE = "leathercase"


class BaseAppDetector(ABC):
    """应用检测器基类 - 模板方法模式"""

    def __init__(self, app_type: AppType):
        self.app_type = app_type
        self.timeout = 10
        self.process_monitor = AdbProcessMonitor()

    @abstractmethod
    def get_package_names(self) -> List[str]:
        """获取应用包名列表"""
        pass

    @abstractmethod
    def get_keywords(self) -> List[str]:
        """获取应用关键词列表"""
        pass

    def check_app_opened(self) -> bool:
        """检查应用是否打开 - 模板方法"""
        try:
            log.info(f"检查{self.app_type.value}应用状态")

            # 1. 优先检查活动状态
            if self._check_activity_status():
                return True

            # 2. 检查焦点窗口
            if self._check_focus_window():
                return True

            # 3. 检查进程状态
            if self._check_process_status():
                return True

            log.info(f"未检测到{self.app_type.value}应用")
            return False

        except Exception as e:
            log.error(f"检查{self.app_type.value}应用失败: {e}")
            return False

    def _check_activity_status(self) -> bool:
        """检查活动状态"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                activity_output = result.stdout
                return self._analyze_activity_output(activity_output)

        except Exception as e:
            log.debug(f"检查活动状态失败: {e}")
        return False

    def _check_focus_window(self) -> bool:
        """检查焦点窗口"""
        try:
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows"],
                capture_output=True,
                text=True,
                timeout=self.timeout,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                window_output = result.stdout
                return self._analyze_window_output(window_output)

        except Exception as e:
            log.debug(f"检查焦点窗口失败: {e}")
        return False

    def _check_process_status(self) -> bool:
        """检查进程状态 - 使用优化的AdbProcessMonitor"""
        try:
            packages = self.get_package_names()

            # 使用AdbProcessMonitor的is_package_running方法进行高效检测
            for package in packages:
                # if self.process_monitor.is_package_running(package,use_fast_method=True):
                if self.process_monitor.is_package_actively_running(package):
                    log.info(f"✅ 通过进程监控检测到{self.app_type.value}应用: {package}")
                    return True

            return False

        except Exception as e:
            log.debug(f"检查进程状态失败: {e}")
            return False

    def _analyze_activity_output(self, output: str) -> bool:
        """分析活动输出"""
        packages = self.get_package_names()
        keywords = self.get_keywords()

        # 优先检查包名（更精确）
        for package in packages:
            if package in output:
                if self._verify_activity_resumed(output, package):
                    log.info(f"✅ 通过活动检测到{self.app_type.value}应用: {package}")
                    return True

        # 检查关键词（需要更严格的验证）
        for keyword in keywords:
            if self._verify_keyword_in_activity(output, keyword):
                log.info(f"✅ 通过关键词检测到{self.app_type.value}应用: {keyword}")
                return True

        return False

    def _analyze_window_output(self, output: str) -> bool:
        """分析窗口输出"""
        packages = self.get_package_names()

        lines = output.split('\n')
        for line in lines:
            if "mCurrentFocus=" in line:
                for package in packages:
                    if package in line and "null" not in line:
                        log.info(f"✅ 通过焦点窗口检测到{self.app_type.value}应用: {package}")
                        return True
                break
        return False

    def _analyze_process_output(self, output: str) -> bool:
        """分析进程输出"""
        packages = self.get_package_names()

        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue

            for package in packages:
                if package in line:
                    # 更严格的进程验证
                    if self._verify_process_running(line, package):
                        log.info(f"✅ 通过进程检测到{self.app_type.value}应用: {package}")
                        return True
        return False

    def _verify_process_running(self, process_line: str, package: str) -> bool:
        """验证进程是否真正运行（区分前台应用和后台服务）"""
        parts = process_line.split()

        # 检查进程行格式是否正确
        if len(parts) < 8:
            return False

        # 检查进程状态（S=睡眠, R=运行, D=不可中断睡眠, T=停止）
        # 排除僵尸进程(Z)和其他异常状态
        valid_states = ['S', 'R', 'D', 'T']
        if not any(status in parts for status in valid_states):
            return False

        # 确保包名完全匹配（避免部分匹配）
        process_name = parts[-1] if parts else ""
        if package not in process_name:
            return False

        # 检查是否是主进程（避免检测到子进程或服务进程）
        if package == process_name or process_name.startswith(package):
            # 根据应用类型采用不同的验证策略
            return self._verify_app_process_by_type(package)

        return False

    def _verify_app_process_by_type(self, package: str) -> bool:
        """根据应用类型验证进程 - 简化版本"""
        # 简化验证逻辑，只检查基本的进程状态
        # 复杂的音乐应用后台检测和前台验证逻辑已被移除，因为过度复杂且实际效果有限
        return True  # 如果进程存在，就认为应用在运行

    def _verify_activity_resumed(self, output: str, package: str) -> bool:
        """验证活动是否处于RESUMED状态"""
        lines = output.split('\n')
        for i, line in enumerate(lines):
            if package in line and "ActivityRecord{" in line:
                # 检查后续几行是否有RESUMED状态
                for j in range(i, min(i + 5, len(lines))):
                    if "state=RESUMED" in lines[j] or "RESUMED" in lines[j]:
                        return True
        return False

    def _verify_keyword_in_activity(self, output: str, keyword: str) -> bool:
        """验证关键词是否在活动上下文中出现（更严格的检查）"""
        lines = output.split('\n')
        keyword_lower = keyword.lower()

        for i, line in enumerate(lines):
            line_lower = line.lower()
            if keyword_lower in line_lower:
                # 检查是否在ActivityRecord上下文中
                if "activityrecord{" in line_lower:
                    # 检查是否有RESUMED状态
                    for j in range(i, min(i + 5, len(lines))):
                        if "state=RESUMED" in lines[j].lower() or "resumed" in lines[j].lower():
                            return True

                # 检查是否在应用包名上下文中
                packages = self.get_package_names()
                for package in packages:
                    if package in line_lower:
                        # 如果关键词和包名在同一行，且有活动相关信息
                        if any(activity_keyword in line_lower for activity_keyword in
                               ['activity', 'intent', 'task', 'stack']):
                            return True

        return False


class AppDetector:
    """应用检测器主类 - 使用工厂模式"""

    def __init__(self):
        """初始化应用检测器"""
        self._detectors: Dict[AppType, BaseAppDetector] = {}
        self._init_detectors()

    def _init_detectors(self):
        """初始化各种应用检测器"""
        # 使用更可靠的导入策略
        detectors = {}

        # 使用绝对路径导入
        try:
            # 原有的检测器
            from pages.base.detectors.weather_detector import WeatherDetector
            from pages.base.detectors.camera_detector import CameraDetector
            from pages.base.detectors.settings_detector import SettingsDetector
            from pages.base.detectors.contacts_detector import ContactsDetector
            from pages.base.detectors.facebook_detector import FacebookDetector
            from pages.base.detectors.music_detector import MusicDetector
            from pages.base.detectors.clock_detector import ClockDetector
            from pages.base.detectors.maps_detector import MapsDetector
            from pages.base.detectors.playstore_detector import PlayStoreDetector

            # 新增的检测器 - 第一批
            from pages.base.detectors.questionnaire_detector import QuestionnaireDetector
            from pages.base.detectors.driving_mode_detector import DrivingModeDetector
            from pages.base.detectors.dialer_detector import DialerDetector
            from pages.base.detectors.smart_message_detector import SmartMessageDetector
            from pages.base.detectors.google_contact_detector import GoogleContactDetector
            from pages.base.detectors.google_phone_detector import GooglePhoneDetector
            from pages.base.detectors.google_message_detector import GoogleMessageDetector
            from pages.base.detectors.trans_id_detector import TransIdDetector
            from pages.base.detectors.tpms_detector import TpmsDetector
            from pages.base.detectors.finder_detector import FinderDetector
            from pages.base.detectors.smart_touch_detector import SmartTouchDetector
            from pages.base.detectors.transsion_tips_detector import TranssionTipsDetector
            from pages.base.detectors.recorder_detector import RecorderDetector
            from pages.base.detectors.phone_master_detector import PhoneMasterDetector
            from pages.base.detectors.ella_detector import EllaDetector
            from pages.base.detectors.mol_detector import MolDetector
            from pages.base.detectors.ai_core_model_manager_detector import AiCoreModelManagerDetector
            from pages.base.detectors.ella_screen_recognition_detector import EllaScreenRecognitionDetector
            from pages.base.detectors.demo_mode_detector import DemoModeDetector
            from pages.base.detectors.ai_gallery_detector import AiGalleryDetector

            # 新增的检测器 - 第二批（娱乐和媒体类）
            from pages.base.detectors.clipper_detector import ClipperDetector
            from pages.base.detectors.vishaplayer_detector import VishaplayerDetector
            from pages.base.detectors.flipmusic_detector import FlipmusicDetector
            from pages.base.detectors.notes_detector import NotesDetector
            from pages.base.detectors.smart_switch_detector import SmartSwitchDetector
            from pages.base.detectors.file_manager_detector import FileManagerDetector
            from pages.base.detectors.calendar_detector import CalendarDetector
            from pages.base.detectors.fm_radio_detector import FmRadioDetector
            from pages.base.detectors.smart_scanner_detector import SmartScannerDetector
            from pages.base.detectors.youtube_detector import YouTubeDetector

            # 新增的检测器 - 第三批（系统控制和个性化）
            from pages.base.detectors.common_control_detector import CommonControlDetector
            from pages.base.detectors.personalization_detector import PersonalizationDetector
            from pages.base.detectors.sharevia_detector import ShareviaDetector
            from pages.base.detectors.one_handed_mode_detector import OneHandedModeDetector
            from pages.base.detectors.split_screen_detector import SplitScreenDetector
            from pages.base.detectors.google_pip_detector import GooglePipDetector
            from pages.base.detectors.thunderback_detector import ThunderbackDetector
            from pages.base.detectors.folding_screen_zone_detector import FoldingScreenZoneDetector
            from pages.base.detectors.smart_panel_detector import SmartPanelDetector
            from pages.base.detectors.smart_hub_detector import SmartHubDetector
            from pages.base.detectors.transsion_stylus_detector import TranssionStylusDetector
            from pages.base.detectors.one_tap_button_detector import OneTapButtonDetector
            from pages.base.detectors.micro_intelligence_detector import MicroIntelligenceDetector

            # 新增的检测器 - 第四批（系统设置和默认应用）
            from pages.base.detectors.google_default_detector import GoogleDefaultDetector
            from pages.base.detectors.fw_dual_system_detector import FwDualSystemDetector
            from pages.base.detectors.multi_user_detector import MultiUserDetector
            from pages.base.detectors.childmode_detector import ChildmodeDetector
            from pages.base.detectors.privacy_detector import PrivacyDetector
            from pages.base.detectors.sales_statistics_detector import SalesStatisticsDetector
            from pages.base.detectors.headset_control_detector import HeadsetControlDetector
            from pages.base.detectors.light_effects_detector import LightEffectsDetector
            from pages.base.detectors.removable_special_detector import RemovableSpecialDetector
            from pages.base.detectors.recent_detector import RecentDetector

            # 新增的检测器 - 第五批（界面和主题）
            from pages.base.detectors.wallpaper_detector import WallpaperDetector
            from pages.base.detectors.magazine_service_detector import MagazineServiceDetector
            from pages.base.detectors.launcher_detector import LauncherDetector
            from pages.base.detectors.theme_detector import ThemeDetector
            from pages.base.detectors.global_search_detector import GlobalSearchDetector
            from pages.base.detectors.zero_screen_detector import ZeroScreenDetector
            from pages.base.detectors.smart_assistant_detector import SmartAssistantDetector
            from pages.base.detectors.cute_pet_detector import CutePetDetector
            from pages.base.detectors.global_theme_detector import GlobalThemeDetector

            # 新增的检测器 - 第六批（系统UI和扩展）
            from pages.base.detectors.system_ui_detector import SystemUiDetector
            from pages.base.detectors.os_settings_ext_detector import OsSettingsExtDetector
            from pages.base.detectors.screen_record_detector import ScreenRecordDetector
            from pages.base.detectors.screen_shot_detector import ScreenShotDetector
            from pages.base.detectors.dynamic_bar_detector import DynamicBarDetector
            from pages.base.detectors.flip_external_screen_detector import FlipExternalScreenDetector
            from pages.base.detectors.aod_detector import AodDetector
            from pages.base.detectors.oobe_detector import OobeDetector
            from pages.base.detectors.leathercase_detector import LeathercaseDetector
            from pages.base.detectors.carlcare_detector import CarlcareDetector
            from pages.base.detectors.healthlife_detector import HealthlifeDetector
            from pages.base.detectors.whatsapp_detector import  WhatsAppDetector

            log.debug("✅ 使用绝对路径导入成功 - 所有检测器")

        except ImportError as e:
            log.error(f"绝对路径导入失败: {e}")
            # 如果绝对导入失败，使用动态导入作为备用方案
            return self._dynamic_import_detectors()

        # 创建检测器实例
        try:
            self._detectors = {
                # 原有的检测器
                AppType.WEATHER: WeatherDetector(),
                AppType.CAMERA: CameraDetector(),
                AppType.SETTINGS: SettingsDetector(),
                AppType.CONTACTS: ContactsDetector(),
                AppType.FACEBOOK: FacebookDetector(),
                AppType.MUSIC: MusicDetector(),
                AppType.CLOCK: ClockDetector(),
                AppType.MAPS: MapsDetector(),
                AppType.PLAYSTORE: PlayStoreDetector(),

                # 新增的检测器 - 问卷和驾驶模式
                AppType.QUESTIONNAIRE: QuestionnaireDetector(),
                AppType.DRIVING_MODE: DrivingModeDetector(),

                # 通信类应用
                AppType.DIALER: DialerDetector(),
                AppType.SMART_MESSAGE: SmartMessageDetector(),
                AppType.GOOGLE_CONTACT: GoogleContactDetector(),
                AppType.GOOGLE_PHONE: GooglePhoneDetector(),
                AppType.GOOGLE_MESSAGE: GoogleMessageDetector(),

                # 系统和身份识别
                AppType.TRANS_ID: TransIdDetector(),
                AppType.TPMS: TpmsDetector(),
                AppType.FINDER: FinderDetector(),
                AppType.SMART_TOUCH: SmartTouchDetector(),
                AppType.TRANSSION_TIPS: TranssionTipsDetector(),

                # 工具类应用
                AppType.RECORDER: RecorderDetector(),
                AppType.PHONE_MASTER: PhoneMasterDetector(),
                AppType.NOTES: NotesDetector(),
                AppType.SMART_SWITCH: SmartSwitchDetector(),
                AppType.FILE_MANAGER: FileManagerDetector(),
                AppType.SMART_SCANNER: SmartScannerDetector(),

                # AI和智能助手
                AppType.ELLA: EllaDetector(),
                AppType.MOL: MolDetector(),
                AppType.AI_CORE_MODEL_MANAGER: AiCoreModelManagerDetector(),
                AppType.ELLA_SCREEN_RECOGNITION: EllaScreenRecognitionDetector(),
                AppType.MICRO_INTELLIGENCE: MicroIntelligenceDetector(),

                # 演示和展示
                AppType.DEMO_MODE: DemoModeDetector(),

                # 娱乐和媒体
                AppType.AI_GALLERY: AiGalleryDetector(),
                AppType.CLIPPER: ClipperDetector(),
                AppType.VISHAPLAYER: VishaplayerDetector(),
                AppType.FLIPMUSIC: FlipmusicDetector(),
                AppType.CALENDAR: CalendarDetector(),
                AppType.FM_RADIO: FmRadioDetector(),
                AppType.YOUTUBE: YouTubeDetector(),

                # 系统控制和个性化
                AppType.COMMON_CONTROL: CommonControlDetector(),
                AppType.PERSONALIZATION: PersonalizationDetector(),
                AppType.SHAREVIA: ShareviaDetector(),
                AppType.ONE_HANDED_MODE: OneHandedModeDetector(),
                AppType.SPLIT_SCREEN: SplitScreenDetector(),
                AppType.GOOGLE_PIP: GooglePipDetector(),
                AppType.THUNDERBACK: ThunderbackDetector(),
                AppType.FOLDING_SCREEN_ZONE: FoldingScreenZoneDetector(),
                AppType.SMART_PANEL: SmartPanelDetector(),
                AppType.SMART_HUB: SmartHubDetector(),
                AppType.TRANSSION_STYLUS: TranssionStylusDetector(),
                AppType.ONE_TAP_BUTTON: OneTapButtonDetector(),

                # 系统设置和默认应用
                AppType.GOOGLE_DEFAULT: GoogleDefaultDetector(),
                AppType.FW_DUAL_SYSTEM: FwDualSystemDetector(),
                AppType.MULTI_USER: MultiUserDetector(),
                AppType.CHILDMODE: ChildmodeDetector(),
                AppType.PRIVACY: PrivacyDetector(),
                AppType.SALES_STATISTICS: SalesStatisticsDetector(),
                AppType.HEADSET_CONTROL: HeadsetControlDetector(),
                AppType.LIGHT_EFFECTS: LightEffectsDetector(),
                AppType.REMOVABLE_SPECIAL: RemovableSpecialDetector(),
                AppType.RECENT: RecentDetector(),

                # 界面和主题
                AppType.WALLPAPER: WallpaperDetector(),
                AppType.MAGAZINE_SERVICE: MagazineServiceDetector(),
                AppType.LAUNCHER: LauncherDetector(),
                AppType.THEME: ThemeDetector(),
                AppType.GLOBAL_SEARCH: GlobalSearchDetector(),
                AppType.ZERO_SCREEN: ZeroScreenDetector(),
                AppType.SMART_ASSISTANT: SmartAssistantDetector(),
                AppType.CUTE_PET: CutePetDetector(),
                AppType.GLOBAL_THEME: GlobalThemeDetector(),

                # 系统UI和扩展
                AppType.SYSTEM_UI: SystemUiDetector(),
                AppType.OS_SETTINGS_EXT: OsSettingsExtDetector(),
                AppType.SCREEN_RECORD: ScreenRecordDetector(),
                AppType.SCREEN_SHOT: ScreenShotDetector(),
                AppType.DYNAMIC_BAR: DynamicBarDetector(),
                AppType.FLIP_EXTERNAL_SCREEN: FlipExternalScreenDetector(),
                AppType.AOD: AodDetector(),
                AppType.OOBE: OobeDetector(),
                AppType.LEATHERCASE: LeathercaseDetector(),
                AppType.CARLCARE: CarlcareDetector(),
                AppType.HEALTHLIFE: HealthlifeDetector(),
                AppType.WHATSAPP: WhatsAppDetector(),
            }
            log.debug(f"✅ 成功初始化 {len(self._detectors)} 个检测器")

        except Exception as e:
            log.error(f"创建检测器实例失败: {e}")
            self._detectors = {}

    def _dynamic_import_detectors(self):
        """动态导入检测器 - 备用方案（已简化）"""
        log.info("动态导入功能已简化，仅支持基础检测器")
        # 复杂的动态导入逻辑已被移除，因为实际使用中绝对导入已足够稳定
        self._detectors = {}

    def _get_detector_utils(self):
        """获取DetectorUtils类 - 使用绝对路径导入"""
        # 注意：DetectorUtils 相关功能已被移除，因为高级功能方法不再使用
        log.warning("DetectorUtils 功能已被移除")
        return None

    def get_detector(self, app_type: AppType) -> Optional[BaseAppDetector]:
        """获取指定类型的检测器"""
        return self._detectors.get(app_type)

    def check_app_opened(self, app_type: Union[AppType, str]) -> bool:
        """检查指定类型的应用是否打开"""
        if isinstance(app_type, str):
            try:
                app_type = AppType(app_type)
            except ValueError:
                log.error(f"不支持的应用类型: {app_type}")
                return False

        detector = self.get_detector(app_type)
        if detector:
            return detector.check_app_opened()
        else:
            log.error(f"未找到{app_type.value}应用的检测器")
            return False

    # # ==================== 向后兼容的方法 ====================
    #
    # def check_weather_app_opened(self) -> bool:
    #     """检查天气应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.WEATHER)
    #
    # def check_camera_app_opened(self) -> bool:
    #     """检查相机应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.CAMERA)
    #
    # def check_settings_opened(self) -> bool:
    #     """检查设置应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.SETTINGS)
    #
    # def check_contacts_app_opened(self) -> bool:
    #     """检查联系人应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.CONTACTS)
    #
    # def check_facebook_app_opened(self) -> bool:
    #     """检查Facebook应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.FACEBOOK)
    #
    # def check_music_app_opened(self) -> bool:
    #     """检查音乐应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.MUSIC)
    #
    # def check_google_map_app_opened(self) -> bool:
    #     """检查Google地图应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.MAPS)
    #
    # def check_google_playstore_app_opened(self) -> bool:
    #     """检查Google Play Store应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.PLAYSTORE)
    #
    # def check_visha_app_opened(self,) -> bool:
    #     """检查Visha音乐应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.MUSIC)
    #
    # def check_youtube_app_opened(self,) -> bool:
    #     """检查YouTube视频应用是否打开 - 向后兼容"""
    #     return self.check_app_opened(AppType.YOUTUBE)
    #
    #
    # def check_contacts_app_opened_smart(self) -> bool:
    #     """智能检查联系人应用 - 向后兼容"""
    #     return self.check_app_opened(AppType.CONTACTS)

    def check_notes_app_opened(self) -> bool:
        """智能检查Notes应用 - 向后兼容"""
        return self.check_app_opened(AppType.NOTES)

    def check_gallery_app_opened(self) -> bool:
        """智能检查Aigallery应用 - 向后兼容"""
        return self.check_app_opened(AppType.AI_GALLERY)



# ==================== 测试和使用示例 ====================

if __name__ == '__main__':
    # 简单的测试示例
    detector = AppDetector()
    print(f"✅ AppDetector 初始化成功，共加载 {len(detector._detectors)} 个检测器")

    # 测试基础功能
    print(f"📱 测试应用检测: {detector.check_app_opened(AppType.DIALER)}")

    # 更多测试示例请参考 pages/base/usage_examples.py
