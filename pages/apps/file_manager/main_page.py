"""
文件管理器主页面
专注于页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from core.logger import log


class FileManagerMainPage(CommonPage):
    """文件管理器主页面"""

    def __init__(self):
        """初始化文件管理器主页面"""
        super().__init__("file_manager", "main_page")

        # 初始化页面元素
        self._init_elements()

    def _init_elements(self):
        """初始化页面元素 - 基于文件管理器常见UI结构"""
        
        # ==================== 应用包名验证 ====================
        self.app_package = self.create_element(
            {"packageName": "com.transsion.filemanagerx"},
            "文件管理器应用包"
        )

        # ==================== 顶部工具栏相关元素 ====================
        self.toolbar = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/toolbar"},
            "顶部工具栏"
        )

        self.action_bar = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/action_bar"},
            "操作栏"
        )

        self.title_text = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/title"},
            "标题文本"
        )

        # 搜索按钮
        self.search_button = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/search"},
            "搜索按钮"
        )

        # 更多选项按钮
        self.more_options_button = self.create_element(
            {"description": "More options"},
            "更多选项按钮"
        )

        self.menu_button = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/menu"},
            "菜单按钮"
        )

        # ==================== 导航抽屉相关元素 ====================
        self.drawer_layout = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/drawer_layout"},
            "抽屉布局"
        )

        self.navigation_view = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/navigation_view"},
            "导航视图"
        )

        # ==================== 主内容区域 ====================
        self.main_content = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/main_content"},
            "主内容区域"
        )

        self.content_frame = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/content_frame"},
            "内容框架"
        )

        # ==================== 文件列表相关元素 ====================
        self.file_list = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_list"},
            "文件列表"
        )

        self.recycler_view = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "文件列表RecyclerView"
        )

        self.list_view = self.create_element(
            {"className": "android.widget.ListView"},
            "文件列表ListView"
        )

        # 文件项元素
        self.file_item = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_item"},
            "文件项"
        )

        self.file_icon = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_icon"},
            "文件图标"
        )

        self.file_name = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_name"},
            "文件名"
        )

        self.file_size = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_size"},
            "文件大小"
        )

        self.file_date = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/file_date"},
            "文件日期"
        )

        # ==================== 底部导航栏相关元素 ====================
        self.bottom_navigation = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/bottom_navigation"},
            "底部导航栏"
        )

        # 常见的底部标签
        self.recents_tab = self.create_element(
            {"text": "Recents"},
            "最近文件标签"
        )

        self.browse_tab = self.create_element(
            {"text": "Browse"},
            "浏览标签"
        )

        self.categories_tab = self.create_element(
            {"text": "Categories"},
            "分类标签"
        )

        # 中文标签
        self.recents_tab_cn = self.create_element(
            {"text": "最近"},
            "最近文件标签(中文)"
        )

        self.browse_tab_cn = self.create_element(
            {"text": "浏览"},
            "浏览标签(中文)"
        )

        self.categories_tab_cn = self.create_element(
            {"text": "分类"},
            "分类标签(中文)"
        )

        # ==================== 路径导航相关元素 ====================
        self.breadcrumb = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/breadcrumb"},
            "面包屑导航"
        )

        self.path_bar = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/path_bar"},
            "路径栏"
        )

        self.back_button = self.create_element(
            {"description": "Navigate up"},
            "返回按钮"
        )

        # ==================== 操作按钮相关元素 ====================
        self.fab = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/fab"},
            "浮动操作按钮"
        )

        self.create_folder_button = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/create_folder"},
            "创建文件夹按钮"
        )

        self.paste_button = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/paste"},
            "粘贴按钮"
        )

        # ==================== 存储空间相关元素 ====================
        self.storage_info = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/storage_info"},
            "存储信息"
        )

        self.storage_bar = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/storage_bar"},
            "存储进度条"
        )

        # ==================== 空状态相关元素 ====================
        self.empty_view = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/empty_view"},
            "空状态视图"
        )

        self.empty_text = self.create_element(
            {"resourceId": "com.transsion.filemanagerx:id/empty_text"},
            "空状态文本"
        )

        # ==================== 通用元素 ====================
        self.progress_bar = self.create_element(
            {"className": "android.widget.ProgressBar"},
            "进度条"
        )

        self.text_view = self.create_element(
            {"className": "android.widget.TextView"},
            "文本视图"
        )

        self.image_view = self.create_element(
            {"className": "android.widget.ImageView"},
            "图像视图"
        )

    # ==================== 应用启动和页面管理 ====================

    def start_app(self) -> bool:
        """启动文件管理器应用"""
        try:
            log.info("启动文件管理器应用")

            package_name = "com.transsion.filemanagerx"

            # 方法1: 尝试启动应用
            try:
                self.driver.app_start(package_name)
                log.info(f"尝试启动应用: {package_name}")
                time.sleep(3)

                # 检查应用是否启动成功
                if self._check_app_started(package_name):
                    log.info("✅ 文件管理器应用启动成功")
                    return True
            except Exception as e:
                log.warning(f"应用启动失败: {e}")

            log.error("❌ 文件管理器应用启动失败")
            return False

        except Exception as e:
            log.error(f"启动文件管理器应用异常: {e}")
            return False

    def _check_app_started(self, package_name: str) -> bool:
        """检查应用是否启动成功"""
        try:
            # 检查当前应用包名
            current_app = self.driver.app_current()
            if current_app.get("package") == package_name:
                return True

            # 检查是否存在应用特有元素
            if self.app_package.is_exists():
                return True

            return False

        except Exception as e:
            log.warning(f"检查应用启动状态异常: {e}")
            return False

    def wait_for_page_load(self, timeout: int = 2) -> bool:
        """等待页面加载完成"""
        try:
            log.info(f"等待文件管理器页面加载完成 (超时: {timeout}秒)")

            # 等待主要元素出现
            # if self.main_content.wait_for_element(timeout=timeout):
            if self.recents_tab.wait_for_element(timeout=timeout):
                log.info("✅ 底部Recents按钮出现，页面加载完成")
                return True
            # elif self.file_list.wait_for_element(timeout=5):
            #     log.info("✅ 文件列表已出现，页面加载完成")
            #     return True
            # elif self.recycler_view.wait_for_element(timeout=5):
            #     log.info("✅ RecyclerView已出现，页面加载完成")
            #     return True
            elif self.toolbar.wait_for_element(timeout=2):

                log.info("✅ 工具栏已出现，页面加载完成")
                return True
            else:
                log.error("❌ 页面加载超时，未找到关键元素")
                return False

        except Exception as e:
            log.error(f"等待页面加载异常: {e}")
            return False

    def stop_app(self) -> bool:
        """停止文件管理器应用"""
        try:
            log.info("停止文件管理器应用")
            package_name = "com.transsion.filemanagerx"

            # app_stop方法不返回值，直接执行操作
            self.driver.app_stop(package_name)

            # 等待一下让应用完全停止
            time.sleep(1)

            log.info("✅ 文件管理器应用停止命令已执行")
            return True

        except Exception as e:
            log.error(f"停止文件管理器应用异常: {e}")
            return False

    # ==================== 页面操作方法 ====================

    def refresh_file_list(self) -> bool:
        """
        刷新文件列表（通过下拉刷新）

        Returns:
            bool: 刷新是否成功
        """
        try:
            log.info("执行下拉刷新操作")

            # 方法1: 在文件列表区域执行下拉刷新
            if self.file_list.is_exists():
                bounds = self.file_list.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=1.0)
                    time.sleep(2)
                    log.info("✅ 在文件列表区域执行下拉刷新成功")
                    return True

            # 方法2: 在RecyclerView区域执行下拉刷新
            if self.recycler_view.is_exists():
                bounds = self.recycler_view.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=1.0)
                    time.sleep(2)
                    log.info("✅ 在RecyclerView区域执行下拉刷新成功")
                    return True

            # 方法3: 在主内容区域执行下拉刷新
            if self.main_content.is_exists():
                bounds = self.main_content.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=1.0)
                    time.sleep(2)
                    log.info("✅ 在主内容区域执行下拉刷新成功")
                    return True

            # 方法4: 使用通用下拉刷新方法
            screen_size = self.driver.window_size()
            width = screen_size[0]
            height = screen_size[1]

            start_x = width // 2
            start_y = height // 3
            end_x = start_x
            end_y = height * 2 // 3

            self.driver.swipe(start_x, start_y, end_x, end_y, duration=1.0)
            time.sleep(2)
            log.info("✅ 使用通用方法执行下拉刷新成功")
            return True

        except Exception as e:
            log.error(f"执行下拉刷新失败: {e}")
            return False

    def click_file(self, file_name: str = None, index: int = 0) -> bool:
        """
        点击文件或文件夹

        Args:
            file_name: 文件名，如果指定则按名称查找
            index: 文件索引，默认点击第一个文件

        Returns:
            bool: 点击是否成功
        """
        try:
            if file_name:
                log.info(f"点击文件: {file_name}")
                # 按文件名查找并点击
                file_element = self.create_element(
                    {"text": file_name},
                    f"文件: {file_name}"
                )
                if file_element.is_exists():
                    file_element.click()
                    log.info(f"✅ 点击文件 '{file_name}' 成功")
                    return True
                else:
                    log.warning(f"❌ 未找到文件: {file_name}")
                    return False
            else:
                log.info(f"点击第{index + 1}个文件")
                # 按索引点击文件
                if self.file_item.is_exists():
                    self.file_item.click()
                    log.info("✅ 点击文件项成功")
                    return True
                else:
                    log.warning("❌ 未找到文件项")
                    return False

        except Exception as e:
            log.error(f"点击文件失败: {e}")
            return False

    def long_click_file(self, file_name: str = None, index: int = 0) -> bool:
        """
        长按文件或文件夹（用于选择或显示上下文菜单）

        Args:
            file_name: 文件名，如果指定则按名称查找
            index: 文件索引，默认长按第一个文件

        Returns:
            bool: 长按是否成功
        """
        try:
            if file_name:
                log.info(f"长按文件: {file_name}")
                file_element = self.create_element(
                    {"text": file_name},
                    f"文件: {file_name}"
                )
                if file_element.is_exists():
                    file_element.long_click()
                    log.info(f"✅ 长按文件 '{file_name}' 成功")
                    return True
                else:
                    log.warning(f"❌ 未找到文件: {file_name}")
                    return False
            else:
                log.info(f"长按第{index + 1}个文件")
                if self.file_item.is_exists():
                    self.file_item.long_click()
                    log.info("✅ 长按文件项成功")
                    return True
                else:
                    log.warning("❌ 未找到文件项")
                    return False

        except Exception as e:
            log.error(f"长按文件失败: {e}")
            return False

    def click_search(self) -> bool:
        """点击搜索按钮"""
        try:
            log.info("点击搜索按钮")

            if self.search_button.is_exists():
                self.search_button.click()
                time.sleep(1)
                log.info("✅ 点击搜索按钮成功")
                return True
            else:
                log.warning("❌ 未找到搜索按钮")
                return False

        except Exception as e:
            log.error(f"点击搜索按钮失败: {e}")
            return False

    def click_more_options(self) -> bool:
        """点击更多选项按钮"""
        try:
            log.info("点击更多选项按钮")

            if self.more_options_button.is_exists():
                self.more_options_button.click()
                time.sleep(1)
                log.info("✅ 点击更多选项按钮成功")
                return True
            elif self.menu_button.is_exists():
                self.menu_button.click()
                time.sleep(1)
                log.info("✅ 点击菜单按钮成功")
                return True
            else:
                log.warning("❌ 未找到更多选项按钮")
                return False

        except Exception as e:
            log.error(f"点击更多选项按钮失败: {e}")
            return False

    # ==================== 导航操作方法 ====================

    def switch_to_recents(self) -> bool:
        """切换到最近文件标签页"""
        try:
            log.info("切换到最近文件标签页")

            # 尝试英文标签
            if self.recents_tab.is_exists():
                self.recents_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Recents标签页成功")
                return True
            # 尝试中文标签
            elif self.recents_tab_cn.is_exists():
                self.recents_tab_cn.click()
                time.sleep(2)
                log.info("✅ 切换到最近标签页成功")
                return True
            else:
                log.warning("❌ 未找到最近文件标签页")
                return False

        except Exception as e:
            log.error(f"切换到最近文件标签页失败: {e}")
            return False

    def switch_to_browse(self) -> bool:
        """切换到浏览标签页"""
        try:
            log.info("切换到浏览标签页")

            # 尝试英文标签
            if self.browse_tab.is_exists():
                self.browse_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Browse标签页成功")
                return True
            # 尝试中文标签
            elif self.browse_tab_cn.is_exists():
                self.browse_tab_cn.click()
                time.sleep(2)
                log.info("✅ 切换到浏览标签页成功")
                return True
            else:
                log.warning("❌ 未找到浏览标签页")
                return False

        except Exception as e:
            log.error(f"切换到浏览标签页失败: {e}")
            return False

    def switch_to_categories(self) -> bool:
        """切换到分类标签页"""
        try:
            log.info("切换到分类标签页")

            # 尝试英文标签
            if self.categories_tab.is_exists():
                self.categories_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Categories标签页成功")
                return True
            # 尝试中文标签
            elif self.categories_tab_cn.is_exists():
                self.categories_tab_cn.click()
                time.sleep(2)
                log.info("✅ 切换到分类标签页成功")
                return True
            else:
                log.warning("❌ 未找到分类标签页")
                return False

        except Exception as e:
            log.error(f"切换到分类标签页失败: {e}")
            return False

    def navigate_back(self) -> bool:
        """返回上一级目录"""
        try:
            log.info("返回上一级目录")

            if self.back_button.is_exists():
                self.back_button.click()
                time.sleep(1)
                log.info("✅ 返回上一级目录成功")
                return True
            else:
                # 使用系统返回键
                self.driver.press("back")
                time.sleep(1)
                log.info("✅ 使用系统返回键成功")
                return True

        except Exception as e:
            log.error(f"返回上一级目录失败: {e}")
            return False

    # ==================== 滚动操作方法 ====================

    def scroll_file_list_up(self) -> bool:
        """向上滚动文件列表"""
        try:
            log.info("向上滚动文件列表")

            if self.file_list.is_exists():
                bounds = self.file_list.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[3] - 100
                    end_x = start_x
                    end_y = bounds[1] + 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向上滚动文件列表成功")
                    return True
            elif self.recycler_view.is_exists():
                bounds = self.recycler_view.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[3] - 100
                    end_x = start_x
                    end_y = bounds[1] + 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向上滚动RecyclerView成功")
                    return True
            else:
                # 使用通用滚动方法
                self.swipe_up()
                log.info("✅ 使用通用方法向上滚动成功")
                return True

        except Exception as e:
            log.error(f"向上滚动文件列表失败: {e}")
            return False

    def scroll_file_list_down(self) -> bool:
        """向下滚动文件列表"""
        try:
            log.info("向下滚动文件列表")

            if self.file_list.is_exists():
                bounds = self.file_list.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向下滚动文件列表成功")
                    return True
            elif self.recycler_view.is_exists():
                bounds = self.recycler_view.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向下滚动RecyclerView成功")
                    return True
            else:
                # 使用通用滚动方法
                self.swipe_down()
                log.info("✅ 使用通用方法向下滚动成功")
                return True

        except Exception as e:
            log.error(f"向下滚动文件列表失败: {e}")
            return False

    # ==================== 状态检查方法 ====================

    def is_on_recents_page(self) -> bool:
        """检查是否在最近文件页面"""
        try:
            log.info("检查是否在最近文件页面")

            # 检查最近文件标签是否被选中或存在
            if self.recents_tab.is_exists() or self.recents_tab_cn.is_exists():
                log.info("✅ 当前在最近文件页面")
                return True
            else:
                log.info("❌ 当前不在最近文件页面")
                return False

        except Exception as e:
            log.error(f"检查最近文件页面状态失败: {e}")
            return False

    def has_files(self) -> bool:
        """检查是否有文件"""
        try:
            log.info("检查是否有文件")

            # 检查是否有文件项
            if self.file_item.is_exists():
                log.info("✅ 找到文件")
                return True
            elif self.file_name.is_exists():
                log.info("✅ 找到文件名元素")
                return True
            elif not self.empty_view.is_exists():
                log.info("✅ 没有空状态视图，可能有文件")
                return True
            else:
                log.info("❌ 未找到文件")
                return False

        except Exception as e:
            log.error(f"检查文件状态失败: {e}")
            return False

    def is_loading(self) -> bool:
        """检查是否正在加载"""
        try:
            log.info("检查是否正在加载")

            if self.progress_bar.is_exists():
                log.info("✅ 正在加载")
                return True
            else:
                log.info("❌ 未在加载")
                return False

        except Exception as e:
            log.error(f"检查加载状态失败: {e}")
            return False


if __name__ == '__main__':
    file_manager_page = FileManagerMainPage()
    result = file_manager_page.start_app()
    if result:
        file_manager_page.wait_for_page_load()
        # 切换到最近文件页面并执行刷新
        file_manager_page.switch_to_recents()
        file_manager_page.refresh_file_list()
