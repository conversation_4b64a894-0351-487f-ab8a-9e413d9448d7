"""
AI Gallery Photos页面
专注于页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from core.logger import log


class AiGalleryPhotosPage(CommonPage):
    """AI Gallery Photos页面"""

    def __init__(self):
        """初始化AI Gallery Photos页面"""
        super().__init__("ai_gallery", "photos_page")

        # 初始化页面元素
        self._init_elements()

    def _init_elements(self):
        """初始化页面元素 - 基于实际UI dump的元素ID"""
        
        # ==================== 应用包名验证 ====================
        self.app_package = self.create_element(
            {"packageName": "com.gallery20"},
            "AI Gallery应用包"
        )

        # ==================== 搜索相关元素 ====================
        self.search_main_frame = self.create_element(
            {"resourceId": "com.gallery20:id/search_main_frame_layout"},
            "搜索主框架"
        )

        # ==================== 照片预览相关元素 ====================
        self.photo_preview_frame = self.create_element(
            {"resourceId": "com.gallery20:id/fl_photo_preview"},
            "照片预览框架"
        )

        # ==================== 主视图容器 ====================
        self.view_pager = self.create_element(
            {"resourceId": "com.gallery20:id/view_page"},
            "主视图分页器"
        )

        self.container_panel = self.create_element(
            {"resourceId": "com.gallery20:id/fl_container_panel"},
            "容器面板"
        )

        self.container_content = self.create_element(
            {"resourceId": "com.gallery20:id/container_content"},
            "内容容器"
        )

        # ==================== 顶部工具栏相关元素 ====================
        self.app_bar_layout = self.create_element(
            {"resourceId": "com.gallery20:id/gallery_day_group_app_bar_layout"},
            "应用栏布局"
        )

        self.collapsing_toolbar = self.create_element(
            {"resourceId": "com.gallery20:id/gallery_day_group_collapsing_toolbar_layout"},
            "折叠工具栏"
        )

        self.app_toolbar = self.create_element(
            {"resourceId": "com.gallery20:id/gallery_day_group_app_toolbar"},
            "应用工具栏"
        )

        # 多选模式按钮
        self.select_mode_button = self.create_element(
            {"resourceId": "com.gallery20:id/menu_gallery_day_group_select_mode"},
            "多选模式按钮"
        )

        # 更多选项按钮
        self.more_options_button = self.create_element(
            {"description": "More options"},
            "更多选项按钮"
        )

        # ==================== 照片列表相关元素 ====================
        self.container_fragment = self.create_element(
            {"resourceId": "com.gallery20:id/container_fragment"},
            "容器片段"
        )

        self.fast_scroller = self.create_element(
            {"resourceId": "com.gallery20:id/fastScroller_gallery_day"},
            "快速滚动器"
        )

        self.album_list = self.create_element(
            {"resourceId": "com.gallery20:id/rv_album_list"},
            "相册列表"
        )

        # 时间线标签
        self.timeline_label = self.create_element(
            {"resourceId": "com.gallery20:id/tv_day_group_timeline"},
            "时间线标签"
        )

        # 今天标签
        self.today_label = self.create_element(
            {"text": "Today"},
            "今天标签"
        )

        # 照片封面
        self.photo_cover = self.create_element(
            {"resourceId": "com.gallery20:id/iv_cover"},
            "照片封面"
        )

        # 图片元素（通用）
        self.image_view = self.create_element(
            {"className": "android.widget.ImageView", "description": "Pictures"},
            "图片视图"
        )

        # 滚动轨道
        self.track_view = self.create_element(
            {"resourceId": "com.gallery20:id/trackView"},
            "滚动轨道"
        )

        # ==================== 底部导航栏相关元素 ====================
        self.foot_opt_bar = self.create_element(
            {"resourceId": "com.gallery20:id/foot_opt_bar"},
            "底部操作栏"
        )

        self.foot_opt_bar_root = self.create_element(
            {"resourceId": "com.gallery20:id/fob_root"},
            "底部操作栏根容器"
        )

        self.foot_opt_bar_container = self.create_element(
            {"resourceId": "com.gallery20:id/os_foot_opt_bar_container"},
            "底部操作栏容器"
        )

        # Photos标签页（当前选中）
        self.photos_tab = self.create_element(
            {"text": "Photos"},
            "Photos标签页"
        )

        self.photos_tab_icon = self.create_element(
            {"resourceId": "com.gallery20:id/os_foot_opt_bar_item_icon", "selected": "true"},
            "Photos标签页图标"
        )

        # Albums标签页
        self.albums_tab = self.create_element(
            {"text": "Albums"},
            "Albums标签页"
        )

        # Memories标签页
        self.memories_tab = self.create_element(
            {"text": "Memories"},
            "Memories标签页"
        )

        # Search标签页
        self.search_tab = self.create_element(
            {"text": "Search"},
            "Search标签页"
        )

        # ==================== 通用RecyclerView元素 ====================
        self.recycler_view = self.create_element(
            {"className": "androidx.recyclerview.widget.RecyclerView"},
            "通用RecyclerView"
        )

        # ==================== 通用ViewGroup元素 ====================
        self.clickable_view_group = self.create_element(
            {"className": "android.view.ViewGroup", "clickable": "true"},
            "可点击的ViewGroup"
        )

    # ==================== 应用启动和页面管理 ====================

    def start_app(self) -> bool:
        """启动AI Gallery应用"""
        try:
            log.info("启动AI Gallery应用")

            package_name = "com.gallery20"
            activity_name = "com.gallery20.HomeActivity"

            # 方法1: 尝试启动指定Activity
            try:
                self.driver.app_start(package_name, activity_name)
                log.info(f"尝试启动Activity: {activity_name}")
                time.sleep(3)

                # 检查应用是否启动成功
                if self._check_app_started(package_name):
                    log.info("✅ AI Gallery应用启动成功（指定Activity）")
                    return True
            except Exception as e:
                log.warning(f"指定Activity启动失败: {e}")

            # 方法2: 备选方案：使用默认启动方式
            try:
                self.driver.app_start(package_name)
                log.info("尝试默认方式启动应用")
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ AI Gallery应用启动成功（默认方式）")
                    return True
            except Exception as e:
                log.warning(f"默认启动方式失败: {e}")

            log.error("❌ AI Gallery应用启动失败")
            return False

        except Exception as e:
            log.error(f"启动AI Gallery应用异常: {e}")
            return False

    def _check_app_started(self, package_name: str) -> bool:
        """检查应用是否启动成功"""
        try:
            # 检查当前应用包名
            current_app = self.driver.app_current()
            if current_app.get("package") == package_name:
                return True

            # 检查是否存在应用特有元素
            if self.app_package.is_exists():
                return True

            return False

        except Exception as e:
            log.warning(f"检查应用启动状态异常: {e}")
            return False

    def wait_for_page_load(self, timeout: int = 15) -> bool:
        """等待页面加载完成"""
        try:
            log.info(f"等待AI Gallery Photos页面加载完成 (超时: {timeout}秒)")

            # 等待底部导航栏出现
            if self.foot_opt_bar.wait_for_element(timeout=timeout):
                log.info("✅ 底部导航栏已出现，页面加载完成")
                return True
            elif self.photos_tab.wait_for_element(timeout=5):
                log.info("✅ Photos标签已出现，页面加载完成")
                return True
            elif self.app_bar_layout.wait_for_element(timeout=5):
                log.info("✅ 应用栏已出现，页面加载完成")
                return True
            else:
                log.error("❌ 页面加载超时，未找到关键元素")
                return False

        except Exception as e:
            log.error(f"等待页面加载异常: {e}")
            return False

    def stop_app(self) -> bool:
        """停止AI Gallery应用"""
        try:
            log.info("停止AI Gallery应用")
            package_name = "com.gallery20"

            # app_stop方法不返回值，直接执行操作
            self.driver.app_stop(package_name)

            # 等待一下让应用完全停止
            time.sleep(1)

            log.info("✅ AI Gallery应用停止命令已执行")
            return True

        except Exception as e:
            log.error(f"停止AI Gallery应用异常: {e}")
            return False

    # ==================== 页面操作方法 ====================

    def click_photo(self, index: int = 0) -> bool:
        """
        点击照片
        
        Args:
            index: 照片索引，默认点击第一张
            
        Returns:
            bool: 点击是否成功
        """
        try:
            log.info(f"点击第{index + 1}张照片")

            # 方法1: 点击照片封面
            if self.photo_cover.is_exists():
                self.photo_cover.click()
                log.info("✅ 点击照片封面成功")
                return True

            # 方法2: 点击可点击的ViewGroup
            if self.clickable_view_group.is_exists():
                self.clickable_view_group.click()
                log.info("✅ 点击ViewGroup成功")
                return True

            # 方法3: 点击图片视图
            if self.image_view.is_exists():
                self.image_view.click()
                log.info("✅ 点击图片视图成功")
                return True

            log.warning("❌ 未找到可点击的照片元素")
            return False

        except Exception as e:
            log.error(f"点击照片失败: {e}")
            return False

    def enter_select_mode(self) -> bool:
        """进入多选模式"""
        try:
            log.info("进入多选模式")

            if self.select_mode_button.is_exists():
                self.select_mode_button.click()
                time.sleep(1)
                log.info("✅ 进入多选模式成功")
                return True
            else:
                log.warning("❌ 未找到多选模式按钮")
                return False

        except Exception as e:
            log.error(f"进入多选模式失败: {e}")
            return False

    def click_more_options(self) -> bool:
        """点击更多选项"""
        try:
            log.info("点击更多选项")

            if self.more_options_button.is_exists():
                self.more_options_button.click()
                time.sleep(1)
                log.info("✅ 点击更多选项成功")
                return True
            else:
                log.warning("❌ 未找到更多选项按钮")
                return False

        except Exception as e:
            log.error(f"点击更多选项失败: {e}")
            return False

    # ==================== 导航操作方法 ====================

    def switch_to_albums(self) -> bool:
        """切换到Albums标签页"""
        try:
            log.info("切换到Albums标签页")

            if self.albums_tab.is_exists():
                self.albums_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Albums标签页成功")
                return True
            else:
                log.warning("❌ 未找到Albums标签页")
                return False

        except Exception as e:
            log.error(f"切换到Albums标签页失败: {e}")
            return False

    def switch_to_memories(self) -> bool:
        """切换到Memories标签页"""
        try:
            log.info("切换到Memories标签页")

            if self.memories_tab.is_exists():
                self.memories_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Memories标签页成功")
                return True
            else:
                log.warning("❌ 未找到Memories标签页")
                return False

        except Exception as e:
            log.error(f"切换到Memories标签页失败: {e}")
            return False

    def switch_to_search(self) -> bool:
        """切换到Search标签页"""
        try:
            log.info("切换到Search标签页")

            if self.search_tab.is_exists():
                self.search_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Search标签页成功")
                return True
            else:
                log.warning("❌ 未找到Search标签页")
                return False

        except Exception as e:
            log.error(f"切换到Search标签页失败: {e}")
            return False

    def switch_to_photos(self) -> bool:
        """切换到Photos标签页"""
        try:
            log.info("切换到Photos标签页")

            if self.photos_tab.is_exists():
                self.photos_tab.click()
                time.sleep(2)
                log.info("✅ 切换到Photos标签页成功")
                return True
            else:
                log.warning("❌ 未找到Photos标签页")
                return False

        except Exception as e:
            log.error(f"切换到Photos标签页失败: {e}")
            return False

    # ==================== 滚动操作方法 ====================

    def scroll_photos_up(self) -> bool:
        """向上滚动照片列表"""
        try:
            log.info("向上滚动照片列表")

            if self.album_list.is_exists():
                # 在相册列表区域向上滑动
                bounds = self.album_list.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[3] - 100
                    end_x = start_x
                    end_y = bounds[1] + 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向上滚动照片列表成功")
                    return True
                else:
                    # 使用通用滚动方法
                    self.swipe_up()
                    log.info("✅ 使用通用方法向上滚动成功")
                    return True
            else:
                log.warning("❌ 未找到相册列表元素")
                return False

        except Exception as e:
            log.error(f"向上滚动照片列表失败: {e}")
            return False

    def scroll_photos_down(self) -> bool:
        """向下滚动照片列表"""
        try:
            log.info("向下滚动照片列表")

            if self.album_list.is_exists():
                # 在相册列表区域向下滑动
                bounds = self.album_list.get_bounds()
                if bounds:
                    start_x = (bounds[0] + bounds[2]) // 2
                    start_y = bounds[1] + 100
                    end_x = start_x
                    end_y = bounds[3] - 100

                    self.driver.swipe(start_x, start_y, end_x, end_y, duration=0.5)
                    log.info("✅ 向下滚动照片列表成功")
                    return True
                else:
                    # 使用通用滚动方法
                    self.swipe_down()
                    log.info("✅ 使用通用方法向下滚动成功")
                    return True
            else:
                log.warning("❌ 未找到相册列表元素")
                return False

        except Exception as e:
            log.error(f"向下滚动照片列表失败: {e}")
            return False

    # ==================== 状态检查方法 ====================

    def is_on_photos_page(self) -> bool:
        """检查是否在Photos页面"""
        try:
            log.info("检查是否在Photos页面")

            # 检查Photos标签是否被选中
            if self.photos_tab_icon.is_exists():
                log.info("✅ 当前在Photos页面")
                return True
            elif self.photos_tab.is_exists():
                log.info("✅ 找到Photos标签，当前在Photos页面")
                return True
            else:
                log.info("❌ 当前不在Photos页面")
                return False

        except Exception as e:
            log.error(f"检查Photos页面状态失败: {e}")
            return False

    def has_photos(self) -> bool:
        """检查是否有照片"""
        try:
            log.info("检查是否有照片")

            # 检查是否有照片封面
            if self.photo_cover.is_exists():
                log.info("✅ 找到照片")
                return True
            elif self.image_view.is_exists():
                log.info("✅ 找到图片视图")
                return True
            elif self.today_label.is_exists():
                log.info("✅ 找到今天标签，可能有照片")
                return True
            else:
                log.info("❌ 未找到照片")
                return False

        except Exception as e:
            log.error(f"检查照片状态失败: {e}")
            return False


if __name__ == '__main__':
    photos_page = AiGalleryPhotosPage()
    result = photos_page.start_app()
    if result:
        photos_page.wait_for_page_load()
        photos_page.click_photo()
