"""
Ella语音助手浮窗页面
专注于浮窗模式下的页面元素定义和基本页面操作
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector, AppType
from pages.apps.ella.ella_response_handler import EllaResponseHandler
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from pages.apps.ella.ella_multimodal_handler import EllaMultimodalHandler
from tools.adb_process_monitor import AdbProcessMonitor
from core.logger import log


class EllaFloatingPage(CommonPage):
    """Ella语音助手浮窗页面"""

    def __init__(self):
        """初始化Ella浮窗页面"""
        super().__init__("ella", "floating_page")

        # 初始化页面元素
        self._init_elements()

        # 初始化功能模块
        self._init_modules()

    def _init_elements(self):
        """初始化浮窗页面元素 - 基于实际DOM结构"""
        # 浮窗根容器
        self.floating_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_root"},
            "浮窗根容器"
        )

        # 语音输入切到键盘输入 (实际定位)
        self.floating_btn_keyboard = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_keyboard"},
            "语音输入切到键盘输入"
        )

        # 浮窗输入框 (实际定位)
        self.floating_input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_input"},
            "浮窗输入框"
        )


        # 备选浮窗输入框
        self.floating_text_input = self.create_element(
            {"className": "android.widget.EditText"},
            "浮窗文本输入框(备选)"
        )

        # 输入框容器
        self.floating_input_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_input"},
            "浮窗输入框容器"
        )

        # 浮窗语音输入按钮 (实际定位)
        self.floating_voice_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_voice"},
            "浮窗语音输入按钮"
        )

        # 键盘按钮 (用于从语音模式切换到文本模式)
        self.keyboard_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_keyboard"},
            "键盘按钮"
        )

        # 发送按钮 (文本模式下的发送按钮)
        self.send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_send"},
            "发送按钮"
        )


        # 确认按钮 (另一种可能的发送按钮)
        self.confirm_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_confirm"},
            "确认按钮"
        )

        self.floating_send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_send"},
            "发送按钮"
        )

        # DeepSeek按钮 (实际定位)
        self.deepseek_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_deep_seek"},
            "DeepSeek按钮"
        )

        # 问候语文本 (实际定位)
        self.greeting_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_hello"},
            "问候语文本"
        )

        # 浮窗展开按钮
        self.floating_expand_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_expand"},
            "浮窗展开按钮"
        )

        # 提示文本 (实际定位)
        self.hint_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_hint"},
            "输入提示文本"
        )

        # 推荐卡片列表 (实际定位)
        self.recommend_card_list = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_card_recommend_new"},
            "推荐卡片列表"
        )

        # 推荐项目 (实际定位)
        self.recommend_item = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/ll_item_recommend"},
            "推荐项目"
        )

        # 推荐项目文本
        self.recommend_item_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_item_recommend"},
            "推荐项目文本"
        )



        # 输入布局容器
        self.input_layout_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rl_input_layout"},
            "输入布局容器"
        )

        # 输入根容器
        self.input_root_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rl_root"},
            "输入根容器"
        )

        # 背景视图
        self.background_view = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/v_bg"},
            "背景视图"
        )

        # 通用文本视图（用于获取浮窗内容）
        self.floating_text_view = self.create_element(
            {"className": "android.widget.TextView"},
            "浮窗通用文本视图"
        )

        # 智能面板浮窗 (右侧小浮窗)
        self.smart_panel_floating = self.create_element(
            {"resourceId": "com.transsion.smartpanel:id/floating_view"},
            "智能面板浮窗"
        )

        # 响应区域相关元素
        self.floating_response_area = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_response"},
            "浮窗响应区域"
        )

        # 聊天列表
        self.floating_chat_list = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_chat"},
            "浮窗聊天列表"
        )

        # 备选响应区域（通用TextView）
        self.floating_response_text = self.create_element(
            {"className": "android.widget.TextView", "text": ".*"},
            "浮窗响应文本(备选)"
        )

        # ==================== Ask Screen 联系人相关元素 ====================

        # 联系人名称输入框
        self.contact_name_input = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_contact_name"},
            "联系人名称输入框"
        )

        # 联系人名称文本显示
        self.contact_name_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_contact_name"},
            "联系人名称文本"
        )

        # 手机号码输入框
        self.phone_number_input = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_phone_number"},
            "手机号码输入框"
        )

        # 手机号码文本显示
        self.phone_number_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_phone_number"},
            "手机号码文本"
        )

        # 识别到的号码显示区域
        self.recognized_number_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_recognized_number"},
            "识别到的号码文本"
        )

        # 联系人信息容器
        self.contact_info_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/ll_contact_info"},
            "联系人信息容器"
        )

        # 保存联系人按钮
        self.save_contact_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_save_contact"},
            "保存联系人按钮"
        )

        # 添加到联系人按钮
        self.add_to_contacts_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_add_to_contacts"},
            "添加到联系人按钮"
        )

        # 联系人操作按钮容器
        self.contact_actions_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/ll_contact_actions"},
            "联系人操作按钮容器"
        )

        # 通用联系人相关文本（备选定位器）
        self.contact_related_text = self.create_element(
            {"className": "android.widget.TextView", "textContains": "contact"},
            "联系人相关文本(备选)"
        )

        # 通用号码相关文本（备选定位器）
        self.number_related_text = self.create_element(
            {"className": "android.widget.TextView", "textMatches": ".*\\d{3,}.*"},
            "号码相关文本(备选)"
        )

        # 联系人卡片容器
        self.contact_card_container = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/card_contact"},
            "联系人卡片容器"
        )

        # 号码确认对话框
        self.number_confirm_dialog = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/dialog_number_confirm"},
            "号码确认对话框"
        )

        # 确认号码按钮
        self.confirm_number_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_confirm_number"},
            "确认号码按钮"
        )

        # 取消号码按钮
        self.cancel_number_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/btn_cancel_number"},
            "取消号码按钮"
        )

    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用 (更新为实际DOM结构)
        self.page_elements = {
            'floating_container': self.floating_container,
            'floating_input_box': self.floating_input_box,
            'floating_text_input': self.floating_text_input,
            'floating_input_container': self.floating_input_container,
            'floating_voice_button': self.floating_voice_button,
            'send_button': self.send_button,
            'keyboard_button': self.keyboard_button,
            'floating_expand_button': self.floating_expand_button,
            'deepseek_button': self.deepseek_button,
            'greeting_text': self.greeting_text,
            'hint_text': self.hint_text,
            'recommend_card_list': self.recommend_card_list,
            'recommend_item': self.recommend_item,
            'recommend_item_text': self.recommend_item_text,
            'input_layout_container': self.input_layout_container,
            'input_root_container': self.input_root_container,
            'background_view': self.background_view,
            'floating_text_view': self.floating_text_view,
            'smart_panel_floating': self.smart_panel_floating,
            'floating_response_area': self.floating_response_area,
            'floating_chat_list': self.floating_chat_list,
            'floating_response_text': self.floating_response_text,

            # Ask Screen 联系人相关元素
            'contact_name_input': self.contact_name_input,
            'contact_name_text': self.contact_name_text,
            'phone_number_input': self.phone_number_input,
            'phone_number_text': self.phone_number_text,
            'recognized_number_text': self.recognized_number_text,
            'contact_info_container': self.contact_info_container,
            'save_contact_button': self.save_contact_button,
            'add_to_contacts_button': self.add_to_contacts_button,
            'contact_actions_container': self.contact_actions_container,
            'contact_related_text': self.contact_related_text,
            'number_related_text': self.number_related_text,
            'contact_card_container': self.contact_card_container,
            'number_confirm_dialog': self.number_confirm_dialog,
            'confirm_number_button': self.confirm_number_button,
            'cancel_number_button': self.cancel_number_button
        }

        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.process_monitor = AdbProcessMonitor()
        self.response_handler = EllaResponseHandler(self.driver, self.status_checker)
        self.command_executor = EllaCommandExecutor(self.driver, self.page_elements)
        self.multimodal_handler = EllaMultimodalHandler(self.driver, self.page_elements)

    # ==================== 浮窗管理方法 ====================

    def trigger_ella_by_power_key(self, duration: float = 3.0) -> bool:
        """
        通过长按power键唤起Ella浮窗（优化版本，支持自动上滑解锁）

        Args:
            duration: 长按持续时间（秒），默认3秒

        Returns:
            bool: 是否成功唤起浮窗
        """
        try:
            log.info(f"🚀 通过长按power键唤起Ella浮窗（持续{duration}秒）")

            # 1. 智能屏幕准备（点亮+解锁）
            if not self.wake_and_unlock_screen():
                log.error("❌ 无法完成屏幕准备")
                return False

            log.info("✅ 屏幕准备完成")

            # 2. 检查浮窗是否已经存在
            if self.is_floating_window_visible():
                log.info("✅ Ella浮窗已经存在")
                return True

            # 3. 执行长按power键
            log.info(f"执行长按power键（{duration}秒）...")
            if not self._long_press_power_key(duration):
                log.error("❌ 长按power键执行失败")
                return False

            # 4. 等待浮窗出现（分阶段等待）
            log.info("等待Ella浮窗出现...")

            # 第一阶段：快速检查（2秒）
            time.sleep(2)
            if self.is_floating_window_visible():
                log.info("✅ 成功通过长按power键唤起Ella浮窗")
                return True

            # 第二阶段：延迟检查（3秒）
            log.debug("浮窗未立即出现，继续等待...")
            time.sleep(3)
            if self.is_floating_window_visible():
                log.info("✅ 延迟后检测到Ella浮窗")
                return True

            # 第三阶段：最后尝试（2秒）
            log.debug("浮窗仍未出现，最后检查...")
            time.sleep(2)
            if self.is_floating_window_visible():
                log.info("✅ 最终检测到Ella浮窗")
                return True

            # 5. 检查可能的问题
            log.warning("长按power键执行成功，但浮窗未出现")

            # 检查屏幕是否被重新锁定
            if self.is_screen_locked_with_fallback():
                log.warning("⚠️ 屏幕可能被重新锁定")

            # 检查当前应用状态
            try:
                current_app = self.driver.app_current()
                current_package = current_app.get('package', '') if current_app else ''
                log.info(f"当前应用包名: {current_package}")
            except Exception as e:
                log.debug(f"无法获取当前应用信息: {e}")

            log.error("❌ 长按power键未能唤起Ella浮窗")
            return False

        except Exception as e:
            log.error(f"通过长按power键唤起Ella浮窗异常: {e}")
            return False

    def _smart_screen_preparation(self) -> bool:
        """智能屏幕准备（调用CommonPage的通用方法）"""
        try:
            log.info("🔍 开始屏幕准备...")

            # 调用CommonPage的通用亮屏+解锁方法
            if not self.wake_and_unlock_screen():
                log.error("❌ 亮屏+解锁失败")
                return False

            log.info("✅ 屏幕准备完成")
            return True

        except Exception as e:
            log.error(f"❌ 屏幕准备异常: {e}")
            return False


    def _swipe_to_unlock(self) -> bool:
        """执行上滑解锁操作（简化版本）"""
        try:
            log.info("📱 执行上滑解锁...")

            # 获取屏幕尺寸
            screen_size = self.get_screen_size()
            width, height = screen_size
            log.debug(f"屏幕尺寸: {width}x{height}")

            # 直接执行标准上滑解锁（最常用的方式）
            start_x = width // 2
            start_y = height * 4 // 5  # 从屏幕下方4/5处开始
            end_y = height // 5        # 滑动到屏幕上方1/5处

            log.info(f"🔄 执行上滑解锁: ({start_x}, {start_y}) → ({start_x}, {end_y})")
            self.driver.swipe(start_x, start_y, start_x, end_y, 0.5)
            time.sleep(1)  # 等待解锁动画完成

            log.info("✅ 上滑解锁操作完成")
            return True

        except Exception as e:
            log.error(f"❌ 上滑解锁操作异常: {e}")
            return False

    def _ensure_screen_on(self) -> bool:
        """
        确保屏幕是亮着的并且已解锁（增强版本，严格验证）

        Returns:
            bool: 操作是否成功
        """
        try:
            log.info("🔍 开始确保屏幕亮起并解锁...")

            # 1. 确保屏幕电源开启
            if not self._ensure_screen_power_on_strict():
                log.error("❌ 无法确保屏幕电源开启")
                return False

            # 2. 严格的解锁流程
            max_unlock_cycles = 3  # 最多3个完整的解锁周期
            for cycle in range(1, max_unlock_cycles + 1):
                log.info(f"🔄 开始第{cycle}轮解锁验证...")

                # 检查当前锁屏状态
                is_locked = self._comprehensive_lock_check()
                log.info(f"🔍 第{cycle}轮锁屏检查结果: {'已锁定' if is_locked else '未锁定'}")

                if not is_locked:
                    # 进行功能性验证确保真正解锁
                    if self._verify_unlock_functionality():
                        log.info(f"✅ 第{cycle}轮验证：屏幕已成功解锁")
                        return True
                    else:
                        log.warning(f"⚠️ 第{cycle}轮功能性验证失败，可能仍处于锁定状态")
                        is_locked = True  # 强制进入解锁流程

                if is_locked:
                    log.info(f"🔓 第{cycle}轮开始解锁操作...")
                    if not self._enhanced_unlock_screen():
                        log.warning(f"⚠️ 第{cycle}轮解锁操作失败")
                        if cycle < max_unlock_cycles:
                            time.sleep(2)  # 等待后重试
                            continue
                        else:
                            log.error("❌ 所有解锁周期都失败了")
                            return False

                    # 解锁后等待生效
                    time.sleep(2)

            log.error("❌ 达到最大解锁周期，仍未成功解锁")
            return False

        except Exception as e:
            log.error(f"❌ 确保屏幕亮起并解锁异常: {e}")
            return False

    def _ensure_screen_power_on_strict(self) -> bool:
        """严格确保屏幕电源开启（优化版本）"""
        try:
            log.info("💡 检查屏幕电源状态...")

            # 多次检查确保准确性
            for attempt in range(3):
                # 使用更宽松的检测方法
                screen_on = self._check_screen_power_status()
                log.info(f"电源状态检查 {attempt + 1}/3: {'开启' if screen_on else '关闭'}")

                if screen_on:
                    log.info("✅ 屏幕电源已开启")
                    return True

                if attempt < 2:  # 不是最后一次尝试
                    log.info(f"⚠️ 屏幕未亮起，尝试点亮（尝试 {attempt + 1}/3）...")
                    self._try_wake_screen()
                    time.sleep(1.5)

            log.warning("⚠️ 多次尝试后屏幕仍未亮起，但继续执行...")
            # 不直接返回False，而是继续执行，让后续的解锁逻辑处理
            return True

        except Exception as e:
            log.error(f"❌ 屏幕电源检查异常: {e}")
            # 异常时也继续执行
            return True

    def _check_screen_power_status(self) -> bool:
        """检查屏幕电源状态（多种方法）"""
        try:
            # 方法1: 检查Display Power状态
            try:
                power_result = self.driver.shell("dumpsys power | grep 'Display Power'")
                log.debug(f"Display Power结果: {power_result}")
                if "ON" in power_result.upper():
                    return True
            except Exception as e:
                log.debug(f"Display Power检查异常: {e}")

            # 方法2: 检查屏幕状态
            try:
                screen_result = self.driver.shell("dumpsys power | grep mScreenOn")
                log.debug(f"mScreenOn结果: {screen_result}")
                if "true" in screen_result.lower():
                    return True
            except Exception as e:
                log.debug(f"mScreenOn检查异常: {e}")

            # 方法3: 检查唤醒状态
            try:
                wake_result = self.driver.shell("dumpsys power | grep mWakefulness")
                log.debug(f"mWakefulness结果: {wake_result}")
                if "awake" in wake_result.lower():
                    return True
            except Exception as e:
                log.debug(f"mWakefulness检查异常: {e}")

            # 方法4: 尝试截图测试（如果能截图说明屏幕可能是亮的）
            try:
                screenshot = self.driver.screenshot()
                if screenshot and hasattr(screenshot, 'size'):
                    width, height = screenshot.size
                    if width > 100 and height > 100:
                        log.debug("截图测试显示屏幕可能已亮起")
                        return True
            except Exception as e:
                log.debug(f"截图测试异常: {e}")

            # 方法5: 检查输入事件（更宽松的检测）
            try:
                input_result = self.driver.shell("dumpsys input | grep 'Display'")
                log.debug(f"输入显示结果: {input_result}")
                # 如果有任何显示相关的输入信息，可能屏幕是亮的
                if input_result and len(input_result.strip()) > 0:
                    return True
            except Exception as e:
                log.debug(f"输入显示检查异常: {e}")

            return False

        except Exception as e:
            log.debug(f"屏幕电源状态检查异常: {e}")
            return False

    def _try_wake_screen(self):
        """尝试唤醒屏幕（多种方法）"""
        try:
            wake_methods = [
                ("power键", "power"),
                ("menu键", "menu"),
                ("home键", "home"),
            ]

            for method_name, key in wake_methods:
                try:
                    log.debug(f"尝试{method_name}唤醒屏幕...")
                    self.driver.press(key)
                    time.sleep(0.3)
                except Exception as e:
                    log.debug(f"{method_name}唤醒失败: {e}")

            # 额外尝试：点击屏幕中心
            try:
                screen_size = self.driver.window_size()
                if isinstance(screen_size, (tuple, list)) and len(screen_size) >= 2:
                    width, height = screen_size[0], screen_size[1]
                elif isinstance(screen_size, dict):
                    width, height = screen_size['width'], screen_size['height']
                else:
                    width, height = 1080, 1920

                center_x, center_y = width // 2, height // 2
                log.debug(f"尝试点击屏幕中心唤醒: ({center_x}, {center_y})")
                self.driver.click(center_x, center_y)

            except Exception as e:
                log.debug(f"点击屏幕中心唤醒失败: {e}")

        except Exception as e:
            log.debug(f"唤醒屏幕异常: {e}")

    def _comprehensive_lock_check(self) -> bool:
        """综合锁屏状态检查"""
        try:
            log.debug("🔍 进行综合锁屏状态检查...")

            lock_indicators = []

            # 检查1: 使用原有的fallback方法
            try:
                if self.is_screen_locked_with_fallback():
                    lock_indicators.append("fallback_method")
            except Exception as e:
                log.debug(f"fallback方法检查异常: {e}")

            # 检查2: 窗口管理器状态
            try:
                wm_result = self.driver.shell("dumpsys window | grep -E '(mShowingLockscreen|KeyguardController|mKeyguardShowing)'")
                if any(pattern in wm_result.lower() for pattern in ["mshowinglock=true", "mkeyguardshowing=true"]):
                    lock_indicators.append("window_manager")
            except Exception as e:
                log.debug(f"窗口管理器检查异常: {e}")

            # 检查3: 活动管理器状态
            try:
                activity_result = self.driver.shell("dumpsys activity | grep -E '(KeyguardController|mKeyguardShowing)'")
                if "mkeyguardshowing=true" in activity_result.lower():
                    lock_indicators.append("activity_manager")
            except Exception as e:
                log.debug(f"活动管理器检查异常: {e}")

            # 检查4: 尝试获取当前焦点窗口
            try:
                focus_result = self.driver.shell("dumpsys window | grep 'mCurrentFocus'")
                if any(pattern in focus_result.lower() for pattern in ["keyguard", "lockscreen", "statusbar"]):
                    lock_indicators.append("focus_window")
            except Exception as e:
                log.debug(f"焦点窗口检查异常: {e}")

            lock_count = len(lock_indicators)
            log.debug(f"锁屏指标数量: {lock_count}, 指标: {lock_indicators}")

            # 如果有2个或以上指标显示锁屏，认为是锁屏状态
            is_locked = lock_count >= 2
            log.debug(f"综合判断结果: {'锁屏' if is_locked else '未锁屏'}")

            return is_locked

        except Exception as e:
            log.warning(f"综合锁屏检查异常: {e}")
            # 异常时保守判断为锁屏状态
            return True

    def _verify_unlock_functionality(self) -> bool:
        """验证解锁功能性（确保真正可用）"""
        try:
            log.debug("🔍 验证解锁功能性...")

            # 测试1: 尝试按home键并检查响应
            try:
                self.driver.press("home")
                time.sleep(1)

                # 检查当前应用
                current_app = self.driver.app_current()
                if current_app and current_app.get('package'):
                    package = current_app.get('package', '')
                    # 如果能获取到正常的应用包名（不是系统UI），说明可能已解锁
                    if package and package != 'com.android.systemui':
                        log.debug(f"✅ 功能性验证1通过，当前应用: {package}")
                    else:
                        log.debug(f"⚠️ 功能性验证1可疑，当前应用: {package}")
                        return False
                else:
                    log.debug("⚠️ 功能性验证1失败，无法获取当前应用")
                    return False

            except Exception as e:
                log.debug(f"功能性验证1异常: {e}")
                return False

            # 测试2: 尝试截图并检查
            try:
                screenshot = self.driver.screenshot()
                if screenshot and hasattr(screenshot, 'size'):
                    width, height = screenshot.size
                    if width > 100 and height > 100:
                        log.debug("✅ 功能性验证2通过，截图正常")
                    else:
                        log.debug("⚠️ 功能性验证2失败，截图尺寸异常")
                        return False
                else:
                    log.debug("⚠️ 功能性验证2失败，无法截图")
                    return False

            except Exception as e:
                log.debug(f"功能性验证2异常: {e}")
                return False

            log.debug("✅ 所有功能性验证通过")
            return True

        except Exception as e:
            log.debug(f"功能性验证异常: {e}")
            return False

    def _enhanced_unlock_screen(self) -> bool:
        """增强的解锁屏幕方法"""
        try:
            log.info("🔓 开始增强解锁流程...")

            # 获取屏幕尺寸
            try:
                screen_size = self.driver.window_size()
                if isinstance(screen_size, tuple) and len(screen_size) >= 2:
                    width, height = screen_size[0], screen_size[1]
                elif isinstance(screen_size, dict):
                    width, height = screen_size['width'], screen_size['height']
                else:
                    log.warning(f"无法解析屏幕尺寸: {screen_size}")
                    width, height = 1080, 1920  # 使用默认值
            except Exception as e:
                log.warning(f"获取屏幕尺寸失败: {e}")
                width, height = 1080, 1920  # 使用默认值

            # 解锁策略列表（按成功率排序）
            unlock_strategies = [
                ("向上滑动解锁", lambda: self._swipe_unlock(width, height)),
                ("按菜单键解锁", lambda: self._key_unlock("menu")),
                ("按返回键解锁", lambda: self._key_unlock("back")),
                ("底部滑动解锁", lambda: self._bottom_swipe_unlock(width, height)),
                ("点击后滑动解锁", lambda: self._click_and_swipe_unlock(width, height)),
                ("双击解锁", lambda: self._double_tap_unlock(width, height)),
            ]

            for strategy_name, strategy_func in unlock_strategies:
                try:
                    log.info(f"🔄 尝试{strategy_name}...")

                    # 先按power键确保屏幕亮起
                    self.driver.press("power")
                    time.sleep(0.5)

                    # 执行解锁策略
                    strategy_func()
                    time.sleep(1.5)  # 等待解锁生效

                    # 检查是否解锁成功
                    if not self._comprehensive_lock_check():
                        log.info(f"✅ {strategy_name}成功")
                        return True
                    else:
                        log.debug(f"⚠️ {strategy_name}未成功")

                except Exception as e:
                    log.debug(f"{strategy_name}异常: {e}")

            log.warning("❌ 所有增强解锁策略都失败了")
            return False

        except Exception as e:
            log.error(f"❌ 增强解锁流程异常: {e}")
            return False

    def _swipe_unlock(self, width: int, height: int):
        """向上滑动解锁"""
        start_x = width // 2
        start_y = height * 3 // 4
        end_y = height // 4
        self.driver.swipe(start_x, start_y, start_x, end_y, 0.5)

    def _key_unlock(self, key: str):
        """按键解锁"""
        self.driver.press(key)

    def _bottom_swipe_unlock(self, width: int, height: int):
        """从底部滑动解锁"""
        start_x = width // 2
        start_y = height - 50
        end_y = height // 3
        self.driver.swipe(start_x, start_y, start_x, end_y, 0.8)

    def _click_and_swipe_unlock(self, width: int, height: int):
        """点击后滑动解锁"""
        center_x, center_y = width // 2, height // 2
        self.driver.click(center_x, center_y)
        time.sleep(0.5)
        self.driver.swipe(center_x, center_y + 100, center_x, center_y - 200, 0.5)

    def _double_tap_unlock(self, width: int, height: int):
        """双击解锁"""
        center_x, center_y = width // 2, height // 2
        self.driver.click(center_x, center_y)
        time.sleep(0.1)
        self.driver.click(center_x, center_y)

    def _is_screen_locked(self) -> bool:
        """
        检查屏幕是否处于锁定状态
        使用多种检测方法提高准确性

        Returns:
            bool: True表示锁定，False表示未锁定
        """
        try:
            log.debug("开始检查屏幕锁定状态...")
            lock_indicators = []  # 收集所有锁屏指示器

            # 方法1: 检查电源管理器状态（最可靠）
            try:
                power_result = self.driver.shell("dumpsys power | grep -E '(mWakefulness|mUserActivityTimeoutOverride|mScreenBrightnessBoostInProgress)'")
                log.debug(f"电源状态: {power_result}")

                # 检查设备是否处于睡眠状态
                if "mWakefulness=Asleep" in power_result or "mWakefulness=Dozing" in power_result:
                    log.debug("通过电源管理器检测到设备处于睡眠状态")
                    lock_indicators.append("power_asleep")

            except Exception as e:
                log.debug(f"电源状态检查异常: {e}")

            # 方法2: 检查窗口管理器状态
            try:
                wm_result = self.driver.shell("dumpsys window | grep -E '(mShowingLockscreen|mKeyguardDrawComplete|mKeyguardGoingAway)'")
                log.debug(f"窗口管理器状态: {wm_result}")

                if "mShowingLockscreen=true" in wm_result:
                    log.debug("通过窗口管理器检测到锁屏")
                    lock_indicators.append("wm_lockscreen")

                if "mKeyguardDrawComplete=false" in wm_result:
                    log.debug("通过窗口管理器检测到键盘锁未完成绘制")
                    lock_indicators.append("wm_keyguard_draw")

            except Exception as e:
                log.debug(f"窗口管理器状态检查异常: {e}")

            # 方法3: 检查活动管理器状态
            try:
                activity_result = self.driver.shell("dumpsys activity | grep -E '(KeyguardController|mKeyguardShowing|mKeyguardGoingAway)'")
                log.debug(f"活动管理器状态: {activity_result}")

                if "mKeyguardShowing=true" in activity_result:
                    log.debug("通过活动管理器检测到锁屏")
                    lock_indicators.append("activity_keyguard")

            except Exception as e:
                log.debug(f"活动管理器状态检查异常: {e}")

            # 方法4: 检查信任管理器状态
            try:
                trust_result = self.driver.shell("dumpsys trust")
                log.debug(f"信任管理器状态: {trust_result[:200]}...")  # 只记录前200字符

                if "mDeviceLockedForUser" in trust_result and "true" in trust_result:
                    log.debug("通过信任管理器检测到设备锁定")
                    lock_indicators.append("trust_locked")

            except Exception as e:
                log.debug(f"信任管理器状态检查异常: {e}")

            # 方法5: 检查当前焦点窗口
            try:
                focus_result = self.driver.shell("dumpsys window | grep 'mCurrentFocus'")
                log.debug(f"当前焦点窗口: {focus_result}")

                focus_lock_indicators = [
                    "Keyguard", "LockScreen", "keyguard", "lockscreen",
                    "StatusBar", "NotificationShade", "AODService",
                    "com.android.systemui/.keyguard",
                    "com.android.systemui/.doze"
                ]

                for indicator in focus_lock_indicators:
                    if indicator in focus_result:
                        log.debug(f"通过焦点窗口检测到锁屏: {indicator}")
                        lock_indicators.append(f"focus_{indicator}")
                        break

            except Exception as e:
                log.debug(f"焦点窗口检查异常: {e}")

            # 方法6: 检查屏幕状态和交互能力
            try:
                # 尝试获取屏幕截图来判断是否可以正常交互
                screenshot_test = self._test_screen_interaction()
                if not screenshot_test:
                    log.debug("通过屏幕交互测试检测到可能的锁屏状态")
                    lock_indicators.append("interaction_failed")

            except Exception as e:
                log.debug(f"屏幕交互测试异常: {e}")

            # 方法7: 检查当前应用和Activity状态
            try:
                current_app = self.driver.app_current()
                current_package = current_app.get('package', '') if current_app else ''
                current_activity = current_app.get('activity', '') if current_app else ''

                log.debug(f"当前应用: {current_package}, Activity: {current_activity}")

                # 检查是否是锁屏相关的包名和Activity
                lock_packages = [
                    'com.android.systemui',
                    'com.android.keyguard',
                    'com.android.settings'  # 某些设备的锁屏设置
                ]

                lock_activities = [
                    'KeyguardActivity',
                    'LockScreenActivity',
                    'keyguard',
                    'lockscreen',
                    'AODService',
                    'DozeService'
                ]

                if current_package in lock_packages:
                    log.debug(f"通过应用包名检测到锁屏相关应用: {current_package}")
                    lock_indicators.append(f"package_{current_package}")

                for activity in lock_activities:
                    if activity.lower() in current_activity.lower():
                        log.debug(f"通过Activity检测到锁屏相关活动: {activity}")
                        lock_indicators.append(f"activity_{activity}")
                        break

            except Exception as e:
                log.debug(f"应用状态检查异常: {e}")
                # 无法获取应用信息可能表示锁屏状态
                lock_indicators.append("app_info_unavailable")

            # 综合判断
            lock_count = len(lock_indicators)
            log.debug(f"锁屏指示器数量: {lock_count}, 指示器: {lock_indicators}")

            # 如果有2个或以上的锁屏指示器，认为是锁屏状态
            if lock_count >= 2:
                log.info(f"✅ 检测到锁屏状态 (指示器数量: {lock_count})")
                return True
            elif lock_count == 1:
                # 如果只有一个指示器，检查是否是强指示器
                strong_indicators = ["power_asleep", "wm_lockscreen", "activity_keyguard", "trust_locked"]
                if any(indicator in strong_indicators for indicator in lock_indicators):
                    log.info(f"✅ 通过强指示器检测到锁屏状态: {lock_indicators[0]}")
                    return True
                else:
                    log.debug(f"弱指示器，不确定锁屏状态: {lock_indicators[0]}")
                    return False
            else:
                log.debug("未检测到锁屏状态")
                return False

        except Exception as e:
            log.warning(f"检查锁屏状态异常: {e}")
            # 异常情况下假设未锁定，避免误判
            return False

    def _test_screen_interaction(self) -> bool:
        """
        测试屏幕交互能力来判断是否锁屏

        Returns:
            bool: True表示可以正常交互，False表示可能锁屏
        """
        try:
            # 方法1: 尝试获取屏幕截图
            try:
                # 使用uiautomator2的截图功能
                screenshot = self.driver.screenshot()
                if screenshot is None:
                    log.debug("截图获取失败，可能处于锁屏状态")
                    return False

                # 检查截图尺寸是否合理
                if hasattr(screenshot, 'size'):
                    width, height = screenshot.size
                    if width < 100 or height < 100:
                        log.debug("截图尺寸异常，可能处于锁屏状态")
                        return False

            except Exception as e:
                log.debug(f"截图测试失败: {e}")
                return False

            # 方法2: 尝试获取UI层次结构
            try:
                # 获取当前页面的UI dump
                ui_dump = self.driver.dump_hierarchy()
                if not ui_dump or len(ui_dump) < 100:
                    log.debug("UI层次结构获取失败或过于简单，可能处于锁屏状态")
                    return False

                # 检查是否包含锁屏相关的UI元素
                lock_ui_indicators = [
                    'android:id/lock_screen',
                    'com.android.systemui:id/keyguard',
                    'android:id/keyguard',
                    'lock_pattern_view',
                    'password_entry',
                    'pin_entry'
                ]

                for indicator in lock_ui_indicators:
                    if indicator in ui_dump:
                        log.debug(f"UI层次结构中发现锁屏元素: {indicator}")
                        return False

            except Exception as e:
                log.debug(f"UI层次结构测试失败: {e}")
                return False

            # 方法3: 尝试获取窗口信息
            try:
                window_info = self.driver.window_size()
                if not window_info:
                    log.debug("窗口信息获取失败，可能处于锁屏状态")
                    return False

            except Exception as e:
                log.debug(f"窗口信息测试失败: {e}")
                return False

            log.debug("屏幕交互测试通过")
            return True

        except Exception as e:
            log.debug(f"屏幕交互测试异常: {e}")
            return False

    def _is_screen_locked_simple(self) -> bool:
        """
        简化版锁屏检测方法（备选方案）
        使用最可靠的几种检测方式

        Returns:
            bool: True表示锁定，False表示未锁定
        """
        try:
            log.debug("使用简化方法检查锁屏状态...")

            # 方法1: 检查设备是否可以执行基本操作
            try:
                # 尝试按Home键，如果锁屏状态下通常不会有响应或响应异常
                self.driver.press("home")
                time.sleep(0.5)

                # 检查当前应用是否变化到桌面
                current_app = self.driver.app_current()
                current_package = current_app.get('package', '') if current_app else ''

                # 如果按Home键后仍然是系统UI或无法获取应用信息，可能是锁屏
                if not current_package or current_package == 'com.android.systemui':
                    log.debug("按Home键后应用状态异常，可能处于锁屏")
                    return True

            except Exception as e:
                log.debug(f"Home键测试失败: {e}")
                return True  # 无法执行基本操作，可能锁屏

            # 方法2: 检查电源管理状态
            try:
                power_result = self.driver.shell("dumpsys power | grep 'mWakefulness='")
                if "Asleep" in power_result or "Dozing" in power_result:
                    log.debug("设备处于睡眠状态")
                    return True
            except Exception as e:
                log.debug(f"电源状态检查失败: {e}")

            # 方法3: 检查是否可以获取屏幕内容
            try:
                # 尝试查找一些常见的UI元素
                ui_elements = self.driver(className="android.widget.TextView")
                if not ui_elements.exists():
                    log.debug("无法找到基本UI元素，可能处于锁屏")
                    return True
            except Exception as e:
                log.debug(f"UI元素检查失败: {e}")
                return True

            # 方法4: 检查窗口管理器的关键状态
            try:
                wm_result = self.driver.shell("dumpsys window | grep 'mShowingLockscreen='")
                if "mShowingLockscreen=true" in wm_result:
                    log.debug("窗口管理器显示锁屏状态")
                    return True
            except Exception as e:
                log.debug(f"窗口管理器检查失败: {e}")

            log.debug("简化检测未发现锁屏状态")
            return False

        except Exception as e:
            log.warning(f"简化锁屏检测异常: {e}")
            return False

    def is_screen_locked_with_fallback(self) -> bool:
        """
        带降级策略的锁屏检测方法
        先使用完整检测，失败时使用简化检测

        Returns:
            bool: True表示锁定，False表示未锁定
        """
        try:
            # 首先尝试完整的检测方法
            result = self._is_screen_locked()
            log.debug(f"完整检测结果: {result}")
            return result

        except Exception as e:
            log.warning(f"完整锁屏检测失败，使用简化方法: {e}")

            # 降级到简化检测方法
            try:
                result = self._is_screen_locked_simple()
                log.debug(f"简化检测结果: {result}")
                return result
            except Exception as e2:
                log.error(f"简化锁屏检测也失败: {e2}")
                # 最后的降级策略：假设未锁定
                return False

    def _try_unlock_screen(self) -> bool:
        """
        尝试解锁屏幕，使用多种解锁策略

        Returns:
            bool: 是否成功
        """
        try:
            log.info("尝试解锁屏幕...")

            # 获取屏幕尺寸
            screen_size = self.driver.window_size()
            if isinstance(screen_size, tuple) and len(screen_size) >= 2:
                width, height = screen_size[0], screen_size[1]
            elif isinstance(screen_size, dict):
                width, height = screen_size['width'], screen_size['height']
            else:
                log.warning(f"无法解析屏幕尺寸: {screen_size}")
                return False

            # 策略1: 向上滑动解锁（最常见）
            log.debug("尝试向上滑动解锁")
            start_x = width // 2
            start_y = height * 3 // 4
            end_y = height // 4
            self.driver.press("power")
            self.driver.swipe(start_x, start_y, start_x, end_y, 0.5)
            time.sleep(1)

            # 检查是否解锁成功
            if not self._is_screen_locked():
                log.info("✅ 向上滑动解锁成功")
                return True

            # 策略2: 按菜单键或返回键唤醒
            log.debug("尝试按菜单键解锁")
            self.driver.press("menu")
            time.sleep(0.5)

            if not self._is_screen_locked():
                log.info("✅ 菜单键解锁成功")
                return True

            # 策略3: 按返回键
            log.debug("尝试按返回键解锁")
            self.driver.press("back")
            time.sleep(0.5)

            if not self._is_screen_locked():
                log.info("✅ 返回键解锁成功")
                return True

            # 策略4: 从不同位置向上滑动
            log.debug("尝试从屏幕底部中央向上滑动")
            start_x = width // 2
            start_y = height - 50  # 从底部边缘开始
            end_y = height // 3

            self.driver.swipe(start_x, start_y, start_x, end_y, 0.8)
            time.sleep(1)

            if not self._is_screen_locked():
                log.info("✅ 底部滑动解锁成功")
                return True

            # 策略5: 尝试点击屏幕中央然后滑动
            log.debug("尝试点击屏幕中央后滑动")
            center_x, center_y = width // 2, height // 2
            self.driver.click(center_x, center_y)
            time.sleep(0.5)

            # 然后向上滑动
            self.driver.swipe(center_x, center_y + 100, center_x, center_y - 200, 0.5)
            time.sleep(1)

            if not self._is_screen_locked():
                log.info("✅ 点击后滑动解锁成功")
                return True

            # 策略6: 尝试双击屏幕（某些设备支持双击唤醒）
            log.debug("尝试双击屏幕解锁")
            self.driver.click(center_x, center_y)
            time.sleep(0.1)
            self.driver.click(center_x, center_y)
            time.sleep(1)

            if not self._is_screen_locked():
                log.info("✅ 双击解锁成功")
                return True

            log.warning("❌ 所有解锁策略都失败了")
            return False

        except Exception as e:
            log.warning(f"尝试解锁屏幕异常: {e}")
            return False

    def open_floating_window(self) -> bool:
        """打开Ella浮窗"""
        try:
            log.info("尝试打开Ella浮窗")

            # 方法1: 通过长按power键唤起Ella浮窗
            try:
                log.info("方法1: 尝试通过长按power键唤起Ella浮窗")
                # 模拟长按power键 (按住3秒)
                self._long_press_power_key(duration=3.0)
                time.sleep(2)

                if self.is_floating_window_visible():
                    log.info("✅ 通过长按power键成功打开浮窗")
                    return True
            except Exception as e:
                log.warning(f"长按power键打开浮窗失败: {e}")

            # # 方法2: 通过shell命令启动浮窗模式
            # try:
            #     log.info("方法2: 尝试通过shell命令启动浮窗")
            #     cmd = "am start -n com.transsion.aivoiceassistant/.FloatingActivity"
            #     self.driver.shell(cmd)
            #     time.sleep(2)
            #
            #     if self.is_floating_window_visible():
            #         log.info("✅ 通过shell命令打开浮窗成功")
            #         return True
            # except Exception as e:
            #     log.warning(f"shell命令打开浮窗失败: {e}")
            #
            # # 方法3: 通过广播启动浮窗
            # try:
            #     log.info("方法3: 尝试通过广播启动浮窗")
            #     cmd = "am broadcast -a com.transsion.aivoiceassistant.SHOW_FLOATING_WINDOW"
            #     self.driver.shell(cmd)
            #     time.sleep(2)
            #
            #     if self.is_floating_window_visible():
            #         log.info("✅ 通过广播打开浮窗成功")
            #         return True
            # except Exception as e:
            #     log.warning(f"广播打开浮窗失败: {e}")

            # # 方法4: 通过按键组合唤起
            # try:
            #     log.info("方法4: 尝试通过按键组合唤起浮窗")
            #     # 尝试其他可能的按键组合
            #     self._try_key_combinations_for_floating()
            #     time.sleep(2)
            #
            #     if self.is_floating_window_visible():
            #         log.info("✅ 通过按键组合打开浮窗成功")
            #         return True
            # except Exception as e:
            #     log.warning(f"按键组合打开浮窗失败: {e}")

            log.error("❌ 所有方法都无法打开浮窗")
            return False

        except Exception as e:
            log.error(f"打开浮窗异常: {e}")
            return False

    def _long_press_power_key(self, duration: float = 3.0) -> bool:
        """
        模拟长按power键

        Args:
            duration: 长按持续时间（秒）

        Returns:
            bool: 操作是否成功
        """
        try:
            log.info(f"模拟长按power键 {duration}秒")

            # 方法1: 使用adb shell input keyevent长按
            try:
                # 发送长按power键事件（--longpress已经包含了长按逻辑）
                self.driver.shell("input keyevent --longpress KEYCODE_POWER")
                log.info("✅ 长按power键命令执行成功")
                return True
            except Exception as e:
                log.warning(f"longpress命令失败: {e}")

            # 方法2: 使用sendevent模拟长按
            try:
                # 按下power键
                self.driver.shell("sendevent /dev/input/event0 1 116 1")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                time.sleep(duration)
                # 释放power键
                self.driver.shell("sendevent /dev/input/event0 1 116 0")
                self.driver.shell("sendevent /dev/input/event0 0 0 0")
                log.info("✅ sendevent长按power键执行成功")
                return True
            except Exception as e:
                log.warning(f"sendevent命令失败: {e}")

            # 方法3: 使用uiautomator的press方法
            try:
                # 连续按power键模拟长按
                for _ in range(int(duration * 2)):  # 每0.5秒按一次
                    self.driver.press("power")
                    time.sleep(0.5)
                log.info("✅ 连续按power键模拟长按成功")
                return True
            except Exception as e:
                log.warning(f"连续按power键失败: {e}")

            return False

        except Exception as e:
            log.error(f"模拟长按power键异常: {e}")
            return False

    def _try_key_combinations_for_floating(self) -> bool:
        """
        尝试其他可能的按键组合来唤起浮窗

        Returns:
            bool: 是否成功
        """
        try:
            log.info("尝试其他按键组合唤起浮窗")

            # 组合1: Power + Volume Up
            try:
                log.debug("尝试 Power + Volume Up")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_UP")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Up 失败: {e}")

            # 组合2: Power + Volume Down
            try:
                log.debug("尝试 Power + Volume Down")
                self.driver.shell("input keyevent KEYCODE_POWER KEYCODE_VOLUME_DOWN")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"Power + Volume Down 失败: {e}")

            # 组合3: 双击Power键
            try:
                log.debug("尝试双击Power键")
                self.driver.press("power")
                time.sleep(0.3)
                self.driver.press("power")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"双击Power键失败: {e}")

            # 组合4: 长按Home键
            try:
                log.debug("尝试长按Home键")
                self.driver.shell("input keyevent --longpress KEYCODE_HOME")
                time.sleep(1)
                if self.is_floating_window_visible():
                    return True
            except Exception as e:
                log.debug(f"长按Home键失败: {e}")

            return False

        except Exception as e:
            log.error(f"尝试按键组合异常: {e}")
            return False

    def close_floating_window(self) -> bool:
        """关闭Ella浮窗"""
        try:
            log.info("尝试关闭Ella浮窗")

            # 方法1: 按back键关闭浮窗（最简单有效的方式）
            if self.is_floating_window_visible():
                log.debug("检测到浮窗存在，尝试按back键关闭")
                self.driver.press("back")
                time.sleep(1)

                if not self.is_floating_window_visible():
                    log.info("✅ 通过back键成功关闭浮窗")
                    return True
                else:
                    log.debug("第一次back键未关闭浮窗，尝试再次按back键")
                    self.driver.press("back")
                    time.sleep(1)

                    if not self.is_floating_window_visible():
                        log.info("✅ 通过第二次back键成功关闭浮窗")
                        return True

            # 方法2: 尝试点击浮窗外部区域关闭（备用方案）
            try:
                log.debug("直接通过back键关闭")
                self.driver.press("back")
                time.sleep(1)

                # log.debug("尝试点击浮窗外部区域关闭")
                # # 点击屏幕左上角区域（通常在浮窗外部）
                # self.driver.click(100, 100)
                # time.sleep(1)

                if not self.is_floating_window_visible():
                    log.info("✅ 通过点击外部区域成功关闭浮窗")
                    return True
            except Exception as e:
                log.warning(f"点击外部区域关闭浮窗失败: {e}")

            # 方法3: 通过广播关闭（最后备用方案）
            try:
                log.debug("尝试通过广播关闭浮窗")
                cmd = "am broadcast -a com.transsion.aivoiceassistant.HIDE_FLOATING_WINDOW"
                self.driver.shell(cmd)
                time.sleep(1)

                if not self.is_floating_window_visible():
                    log.info("✅ 通过广播成功关闭浮窗")
                    return True
            except Exception as e:
                log.warning(f"广播关闭浮窗失败: {e}")

            log.error("❌ 所有方法都无法关闭浮窗")
            return False

        except Exception as e:
            log.error(f"关闭浮窗异常: {e}")
            return False

    def is_floating_window_visible(self) -> bool:
        """检查浮窗是否可见 - 基于实际DOM结构"""
        try:
            # 方法1: 检查浮窗根容器是否存在
            if self.floating_container.is_exists():
                log.debug("通过浮窗根容器检测到浮窗可见")
                return True

            # 方法2: 检查输入框是否存在
            if self.floating_input_box.is_exists():
                log.debug("通过输入框检测到浮窗可见")
                return True

            # 方法3: 检查问候语文本是否存在
            if self.greeting_text.is_exists():
                log.debug("通过问候语文本检测到浮窗可见")
                return True

            # 方法4: 检查DeepSeek按钮是否存在
            if self.deepseek_button.is_exists():
                log.debug("通过DeepSeek按钮检测到浮窗可见")
                return True

            # 方法5: 通过包名检查
            try:
                current_app = self.driver.app_current()
                if current_app.get('package') == 'com.transsion.aivoiceassistant':
                    log.debug("通过应用包名检测到Ella浮窗")
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            log.error(f"检查浮窗可见性异常: {e}")
            return False

    def minimize_floating_window(self) -> bool:
        """最小化浮窗"""
        try:
            log.info("尝试最小化浮窗")
            
            if self.floating_minimize_button.is_exists():
                self.floating_minimize_button.click()
                time.sleep(1)
                log.info("✅ 浮窗最小化成功")
                return True
            else:
                log.warning("❌ 未找到最小化按钮")
                return False
                
        except Exception as e:
            log.error(f"最小化浮窗异常: {e}")
            return False

    def expand_floating_window(self) -> bool:
        """展开浮窗"""
        try:
            log.info("尝试展开浮窗")
            
            if self.floating_expand_button.is_exists():
                self.floating_expand_button.click()
                time.sleep(1)
                log.info("✅ 浮窗展开成功")
                return True
            else:
                log.warning("❌ 未找到展开按钮")
                return False
                
        except Exception as e:
            log.error(f"展开浮窗异常: {e}")
            return False

    def move_floating_window(self, target_x: int, target_y: int) -> bool:
        """移动浮窗到指定位置"""
        try:
            log.info(f"尝试移动浮窗到位置: ({target_x}, {target_y})")
            
            if self.floating_drag_area.is_exists():
                # 获取当前拖拽区域的位置
                bounds = self.floating_drag_area.get_bounds()
                if bounds:
                    current_x = (bounds['left'] + bounds['right']) // 2
                    current_y = (bounds['top'] + bounds['bottom']) // 2
                    
                    # 执行拖拽操作
                    self.driver.drag(current_x, current_y, target_x, target_y, 0.5)
                    time.sleep(1)
                    
                    log.info("✅ 浮窗移动完成")
                    return True
            else:
                log.warning("❌ 未找到拖拽区域")
                return False
                
        except Exception as e:
            log.error(f"移动浮窗异常: {e}")
            return False

    # ==================== 命令执行方法 ====================

    def execute_text_command(self, command: str):
        """执行文本命令"""
        # self.wake_and_unlock_screen()
        self.open_floating_window()
        return self.execute_text_command_in_floating(command)

    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = "en"):
        """执行语音命令"""
        self.wake_and_unlock_screen()
        self.open_floating_window()
        return self.execute_voice_command_in_floating(command, duration, language)

    def _ensure_text_input_mode(self) -> bool:
        """
        确保当前处于文本输入模式

        Returns:
            bool: 是否成功切换到文本输入模式
        """
        try:
            log.debug("检查当前输入模式...")

            # 检查是否存在键盘按钮
            if self.keyboard_button.is_exists():
                log.info("检测到键盘按钮，当前处于语音模式，需要切换到文本模式")

                # 点击键盘按钮切换到文本输入模式
                self.keyboard_button.click()
                time.sleep(1)  # 等待模式切换完成

                # 再次检查键盘按钮是否消失
                if not self.keyboard_button.is_exists():
                    log.info("✅ 成功切换到文本输入模式")
                    return True
                else:
                    log.warning("⚠️ 键盘按钮仍然存在，可能切换失败")
                    # 尝试再次点击
                    self.keyboard_button.click()
                    time.sleep(1)

                    if not self.keyboard_button.is_exists():
                        log.info("✅ 第二次尝试成功切换到文本输入模式")
                        return True
                    else:
                        log.error("❌ 无法切换到文本输入模式")
                        return False
            else:
                log.debug("未检测到键盘按钮，当前已处于文本输入模式")
                return True

        except Exception as e:
            log.error(f"确保文本输入模式异常: {e}")
            return False

    def _check_current_input_mode(self) -> str:
        """
        检查当前输入模式

        Returns:
            str: 'voice' 表示语音模式, 'text' 表示文本模式, 'unknown' 表示未知
        """
        try:
            if self.keyboard_button.is_exists():
                return 'voice'
            elif self.floating_voice_button.is_exists():
                return 'text'
            else:
                return 'unknown'
        except Exception as e:
            log.error(f"检查输入模式异常: {e}")
            return 'unknown'

    def execute_text_command_in_floating(self, command: str) -> bool:
        """
        在浮窗中执行文本命令（优化版本）
        参考trigger_ella_by_power_key的设计模式，增加分阶段验证和错误处理

        ✨ 新特性：
        - 自动确保浮窗已调起（如果浮窗不存在会自动调起）
        - 分阶段验证和容错处理
        - 多种发送方式的fallback机制
        - 完整的执行结果验证

        Args:
            command: 要执行的文本命令

        Returns:
            bool: 是否成功执行命令
        """
        try:
            log.info(f"🚀 开始在浮窗中执行文本命令: {command}")

            # # 1. 前置条件检查
            # if not self._validate_floating_preconditions():
            #     return False

            # 2. 准备文本输入环境
            if not self._prepare_text_input_environment():
                return False

            # 3. 执行命令输入
            if not self._execute_command_input(command):
                return False

            # 4. 发送命令（分阶段尝试）
            if not self._send_command_with_fallback():
                return False

            # 5. 验证命令执行结果
            return self._verify_command_execution()

        except Exception as e:
            log.error(f"❌ 在浮窗中执行文本命令异常: {e}")
            return False

    def _validate_floating_preconditions(self) -> bool:
        """验证浮窗执行命令的前置条件，确保浮窗已调起"""
        try:
            log.info("📋 步骤1: 验证前置条件...")

            # 1. 确保屏幕状态正常
            if not self._ensure_screen_ready():
                log.error("❌ 屏幕状态不正常")
                return False

            # 2. 确保浮窗已调起
            if not self._ensure_floating_window_active():
                log.error("❌ 无法确保浮窗处于活跃状态")
                return False

            # 3. 验证浮窗功能可用性
            if not self._verify_floating_window_functionality():
                log.error("❌ 浮窗功能验证失败")
                return False

            log.info("✅ 所有前置条件验证通过")
            return True

        except Exception as e:
            log.error(f"❌ 前置条件验证异常: {e}")
            return False

    def _ensure_screen_ready(self) -> bool:
        """确保屏幕状态正常（增强版本，确保屏幕解锁）"""
        try:
            log.info("🔍 检查并确保屏幕状态正常...")

            # 第一阶段：快速检查屏幕状态
            if self._quick_screen_check():
                log.info("✅ 快速检查：屏幕状态正常")
                return True

            # 第二阶段：尝试唤醒和解锁屏幕
            if not self._wake_and_unlock_screen():
                log.error("❌ 无法唤醒和解锁屏幕")
                return False

            # 第三阶段：验证屏幕状态
            if not self._verify_screen_unlocked():
                log.error("❌ 屏幕解锁验证失败")
                return False

            log.info("✅ 屏幕状态确认正常")
            return True

        except Exception as e:
            log.error(f"❌ 屏幕状态检查异常: {e}")
            return False

    def _quick_screen_check(self) -> bool:
        """快速检查屏幕状态"""
        try:
            log.info("🔍 快速检查屏幕状态...")

            # 检查屏幕是否亮起
            power_result = self.driver.shell("dumpsys power | grep 'Display Power'")
            if "ON" not in power_result:
                log.info("⚠️ 屏幕未亮起")
                return False

            # 快速检查是否锁屏
            if self.is_screen_locked_with_fallback():
                log.info("⚠️ 检测到锁屏状态")
                return False

            log.info("✅ 快速检查通过")
            return True

        except Exception as e:
            log.debug(f"快速检查异常: {e}")
            return False

    def _wake_and_unlock_screen(self) -> bool:
        """唤醒和解锁屏幕（增强版本）"""
        try:
            log.info("🔄 开始唤醒和解锁屏幕...")

            # 第一步：确保屏幕亮起
            if not self._ensure_screen_power_on():
                log.error("❌ 无法点亮屏幕")
                return False

            # 第二步：检查并解锁屏幕
            max_unlock_attempts = 3
            for attempt in range(1, max_unlock_attempts + 1):
                log.info(f"🔓 解锁尝试 {attempt}/{max_unlock_attempts}")

                # 检查是否还需要解锁
                if not self.is_screen_locked_with_fallback():
                    log.info("✅ 屏幕已解锁")
                    return True

                # 尝试解锁
                if self._try_unlock_screen():
                    # 等待解锁生效
                    time.sleep(1)
                    if not self.is_screen_locked_with_fallback():
                        log.info(f"✅ 第{attempt}次尝试解锁成功")
                        return True

                # 如果不是最后一次尝试，等待一下再试
                if attempt < max_unlock_attempts:
                    time.sleep(1)

            log.error("❌ 所有解锁尝试都失败了")
            return False

        except Exception as e:
            log.error(f"❌ 唤醒和解锁屏幕异常: {e}")
            return False

    def _ensure_screen_power_on(self) -> bool:
        """确保屏幕电源开启"""
        try:
            log.info("💡 确保屏幕电源开启...")

            # 检查屏幕电源状态
            power_result = self.driver.shell("dumpsys power | grep 'Display Power'")

            if "ON" in power_result:
                log.info("✅ 屏幕已亮起")
                return True

            log.info("⚠️ 屏幕未亮起，尝试点亮...")

            # 尝试多种方式点亮屏幕
            wake_methods = [
                ("按menu键", lambda: self.driver.press("menu")),
                ("按home键", lambda: self.driver.press("home")),
            ]

            for method_name, method_func in wake_methods:
                try:
                    log.info(f"🔄 尝试{method_name}点亮屏幕...")
                    method_func()
                    time.sleep(1)

                    # 检查是否成功点亮
                    power_result = self.driver.shell("dumpsys power | grep 'Display Power'")
                    if "ON" in power_result:
                        log.info(f"✅ {method_name}成功点亮屏幕")
                        return True

                except Exception as e:
                    log.debug(f"{method_name}失败: {e}")

            log.error("❌ 无法点亮屏幕")
            return False

        except Exception as e:
            log.error(f"❌ 确保屏幕电源开启异常: {e}")
            return False

    def _verify_screen_unlocked(self) -> bool:
        """验证屏幕已解锁（增强验证）"""
        try:
            log.info("🔍 验证屏幕解锁状态...")

            # 第一阶段：基本解锁检查
            if self.is_screen_locked_with_fallback():
                log.warning("⚠️ 基本检查显示屏幕仍处于锁定状态")
                return False

            # 第二阶段：功能性验证
            if not self._test_screen_functionality():
                log.warning("⚠️ 屏幕功能性验证失败")
                return False

            # 第三阶段：UI交互验证
            if not self._test_ui_interaction():
                log.warning("⚠️ UI交互验证失败")
                return False

            log.info("✅ 屏幕解锁状态验证通过")
            return True

        except Exception as e:
            log.error(f"❌ 验证屏幕解锁状态异常: {e}")
            return False

    def _test_screen_functionality(self) -> bool:
        """测试屏幕基本功能"""
        try:
            log.debug("🔍 测试屏幕基本功能...")

            # 测试截图功能
            try:
                screenshot = self.driver.screenshot()
                if screenshot is None:
                    log.debug("截图功能异常")
                    return False

                # 检查截图尺寸
                if hasattr(screenshot, 'size'):
                    width, height = screenshot.size
                    if width < 100 or height < 100:
                        log.debug("截图尺寸异常")
                        return False

            except Exception as e:
                log.debug(f"截图测试失败: {e}")
                return False

            log.debug("✅ 屏幕基本功能正常")
            return True

        except Exception as e:
            log.debug(f"屏幕功能测试异常: {e}")
            return False

    def _test_ui_interaction(self) -> bool:
        """测试UI交互能力"""
        try:
            log.debug("🔍 测试UI交互能力...")

            # 测试基本按键响应
            try:
                # 按home键测试
                self.driver.press("home")
                time.sleep(0.5)

                # 检查当前应用状态
                current_app = self.driver.app_current()
                if current_app is None:
                    log.debug("无法获取当前应用信息")
                    return False

            except Exception as e:
                log.debug(f"UI交互测试失败: {e}")
                return False

            log.debug("✅ UI交互能力正常")
            return True

        except Exception as e:
            log.debug(f"UI交互测试异常: {e}")
            return False

    def _ensure_floating_window_active(self) -> bool:
        """确保浮窗已调起并处于活跃状态"""
        try:
            log.info("🔍 确保浮窗已调起...")

            # 第一次检查：浮窗是否已经存在
            if self.is_floating_window_visible():
                log.info("✅ 浮窗已存在")
                return True

            log.info("⚠️ 浮窗不存在，尝试调起浮窗...")

            # 尝试通过长按power键调起浮窗
            if not self.trigger_ella_by_power_key():
                log.error("❌ 通过power键调起浮窗失败")

                # 备选方案：尝试其他调起方式
                log.info("🔄 尝试备选调起方式...")
                if not self._try_alternative_trigger_methods():
                    log.error("❌ 所有调起浮窗的方法都失败了")
                    return False

            # 验证浮窗是否成功调起
            if not self.is_floating_window_visible():
                log.error("❌ 浮窗调起后仍不可见")
                return False

            log.info("✅ 浮窗成功调起并可见")
            return True

        except Exception as e:
            log.error(f"❌ 确保浮窗活跃异常: {e}")
            return False

    def _try_alternative_trigger_methods(self) -> bool:
        """尝试备选的浮窗调起方式"""
        try:
            log.info("🔄 尝试备选调起方式...")

            # 方法1：尝试不同的长按时间
            alternative_durations = [2.0, 4.0, 5.0]
            for duration in alternative_durations:
                log.info(f"🔄 尝试{duration}秒长按power键...")
                if self.trigger_ella_by_power_key(duration):
                    if self.is_floating_window_visible():
                        log.info(f"✅ {duration}秒长按成功调起浮窗")
                        return True
                time.sleep(1)  # 短暂等待

            # 方法2：可以在这里添加其他调起方式，比如语音唤醒等
            # 目前主要依赖power键方式

            return False

        except Exception as e:
            log.error(f"❌ 备选调起方式异常: {e}")
            return False

    def _verify_floating_window_functionality(self) -> bool:
        """验证浮窗功能可用性"""
        try:
            log.info("🔍 验证浮窗功能可用性...")

            # 检查关键元素是否存在
            key_elements_check = [
                ("输入框", lambda: self.floating_input_box.is_exists() or self.floating_text_input.is_exists()),
                ("语音按钮", lambda: self.floating_voice_button.is_exists()),
            ]

            for element_name, check_func in key_elements_check:
                try:
                    if not check_func():
                        log.warning(f"⚠️ {element_name}不可用")
                        # 不直接返回False，因为某些元素可能在特定状态下才出现
                except Exception as e:
                    log.debug(f"检查{element_name}时异常: {e}")

            # 检查当前输入模式
            current_mode = self._check_current_input_mode()
            log.info(f"✅ 当前输入模式: {current_mode}")

            log.info("✅ 浮窗功能验证完成")
            return True

        except Exception as e:
            log.error(f"❌ 浮窗功能验证异常: {e}")
            return False

    def _prepare_text_input_environment(self) -> bool:
        """准备文本输入环境"""
        try:
            log.info("📋 步骤2: 准备文本输入环境...")

            # 确保切换到文本输入模式
            if not self._ensure_text_input_mode():
                log.error("❌ 无法切换到文本输入模式")
                return False
            log.info("✅ 文本输入模式已就绪")

            # 确保输入框可用
            if not self._ensure_floating_input_ready():
                log.error("❌ 浮窗输入框不可用")
                return False
            log.info("✅ 浮窗输入框已就绪")

            return True

        except Exception as e:
            log.error(f"❌ 文本输入环境准备异常: {e}")
            return False

    def _execute_command_input(self, command: str) -> bool:
        """执行命令输入"""
        try:
            log.info("📋 步骤3: 执行命令输入...")

            # 查找可用的输入框
            input_element = self._find_available_input_element()
            if not input_element:
                log.error("❌ 未找到可用的浮窗输入框")
                return False

            # 清空输入框并输入命令
            if not self._clear_and_input_command(input_element, command):
                log.error("❌ 命令输入失败")
                return False

            log.info("✅ 命令输入完成")
            return True

        except Exception as e:
            log.error(f"❌ 命令输入异常: {e}")
            return False

    def _find_available_input_element(self):
        """查找可用的输入框元素"""
        log.info("🔍 查找可用的输入框...")

        input_candidates = [
            ("主输入框", self.floating_input_box),
            ("备选输入框", self.floating_text_input),
        ]

        for name, element in input_candidates:
            try:
                if element.is_exists():
                    log.info(f"✅ 找到{name}")
                    return element
            except Exception as e:
                log.debug(f"检查{name}时异常: {e}")

        return None

    def _clear_and_input_command(self, input_element, command: str) -> bool:
        """清空输入框并输入命令"""
        try:
            # 清空输入框
            log.info("🧹 清空输入框...")
            if not input_element.clear_text():
                log.warning("⚠️ 清空输入框失败，尝试继续输入")
            time.sleep(0.5)

            # 输入命令
            log.info(f"⌨️ 输入命令: {command}")
            if not input_element.send_keys(command):
                log.error("❌ 输入命令失败")
                return False

            log.info("✅ 命令输入成功")
            time.sleep(0.5)
            return True

        except Exception as e:
            log.error(f"❌ 输入命令异常: {e}")
            return False

    def _send_command_with_fallback(self) -> bool:
        """发送命令（分阶段尝试，参考trigger_ella_by_power_key的分阶段验证模式）"""
        try:
            log.info("📋 步骤4: 发送命令...")
            log.info("💡 注意: 文本模式需要点击发送按钮，语音模式会自动发送")

            # 第一阶段：优先使用标准发送按钮
            if self._try_standard_send_button():
                log.info("✅ 第一阶段：标准发送按钮发送成功")
                return True

            # 第二阶段：尝试替代发送按钮
            if self._try_alternative_send_buttons():
                log.info("✅ 第二阶段：替代发送按钮发送成功")
                return True

            # 第三阶段：尝试键盘发送
            if self._try_keyboard_send():
                log.info("✅ 第三阶段：键盘发送成功")
                return True

            # 第四阶段：尝试点击发送
            if self._try_click_send():
                log.info("✅ 第四阶段：点击发送成功")
                return True

            log.error("❌ 所有发送方法都失败了")
            return False

        except Exception as e:
            log.error(f"❌ 发送命令异常: {e}")
            return False

    def _try_standard_send_button(self) -> bool:
        """尝试标准发送按钮"""
        try:
            log.info("🔍 第一阶段：尝试标准发送按钮...")
            if self.send_button.is_exists():
                log.info("📤 找到发送按钮，点击发送...")
                self.send_button.click()
                time.sleep(0.5)  # 等待发送处理
                return True
            else:
                log.info("⚠️ 未找到标准发送按钮")
                return False
        except Exception as e:
            log.warning(f"⚠️ 标准发送按钮点击失败: {e}")
            return False

    def _try_alternative_send_buttons(self) -> bool:
        """尝试替代发送按钮"""
        try:
            log.info("🔍 第二阶段：尝试替代发送按钮...")
            alternative_buttons = [
                ("语音按钮", self.floating_voice_button),
                ("展开按钮", self.floating_expand_button),
            ]

            for button_name, button_element in alternative_buttons:
                try:
                    if button_element.is_exists():
                        log.info(f"📤 找到{button_name}，尝试发送...")
                        button_element.click()
                        time.sleep(0.5)
                        return True
                except Exception as e:
                    log.debug(f"通过{button_name}发送失败: {e}")

            return False
        except Exception as e:
            log.warning(f"⚠️ 替代发送按钮尝试失败: {e}")
            return False

    def _try_keyboard_send(self) -> bool:
        """尝试键盘发送"""
        try:
            log.info("🔍 第三阶段：尝试键盘发送...")
            log.info("⌨️ 按回车键发送...")
            self.driver.press("enter")
            time.sleep(0.5)
            return True
        except Exception as e:
            log.warning(f"⚠️ 键盘发送失败: {e}")
            return False

    def _try_click_send(self) -> bool:
        """尝试点击发送"""
        try:
            log.info("🔍 第四阶段：尝试点击发送...")

            # 获取输入框元素
            input_element = self._find_available_input_element()
            if not input_element:
                return False

            # 尝试点击输入框右侧区域
            try:
                bounds = input_element.get_bounds()
                if bounds and len(bounds) >= 4:
                    x = bounds[2] + 50  # 输入框右边50像素
                    y = (bounds[1] + bounds[3]) // 2  # 输入框中心Y坐标
                    log.info(f"🖱️ 点击输入框右侧位置: ({x}, {y})")
                    self.driver.click(x, y)
                    time.sleep(0.5)
                    return True
            except Exception as e:
                log.debug(f"点击输入框右侧失败: {e}")

            # 尝试点击输入框本身
            try:
                log.info("🖱️ 点击输入框本身...")
                input_element.click()
                time.sleep(0.5)
                return True
            except Exception as e:
                log.debug(f"点击输入框本身失败: {e}")

            return False
        except Exception as e:
            log.warning(f"⚠️ 点击发送失败: {e}")
            return False

    def _verify_command_execution(self) -> bool:
        """验证命令执行结果（参考trigger_ella_by_power_key的验证模式）"""
        try:
            log.info("📋 步骤5: 验证命令执行结果...")

            # 第一阶段：快速检查（1秒）
            time.sleep(1)
            if self._check_command_sent():
                log.info("✅ 第一阶段：命令发送成功")
                return True

            # 第二阶段：延迟检查（2秒）
            log.debug("命令未立即发送，继续等待...")
            time.sleep(2)
            if self._check_command_sent():
                log.info("✅ 第二阶段：延迟后检测到命令发送成功")
                return True

            # 第三阶段：最后检查（1秒）
            log.debug("命令仍未发送，最后检查...")
            time.sleep(1)
            if self._check_command_sent():
                log.info("✅ 第三阶段：最终检测到命令发送成功")
                return True

            log.warning("⚠️ 命令可能未成功发送")
            return False

        except Exception as e:
            log.error(f"❌ 验证命令执行结果异常: {e}")
            return False

    def _check_command_sent(self) -> bool:
        """检查命令是否已发送"""
        try:
            # 检查输入框是否已清空（通常表示命令已发送）
            input_element = self._find_available_input_element()
            if input_element:
                text = input_element.get_text()
                if not text or text.strip() == "":
                    return True

            # 可以添加其他检查逻辑，比如检查是否出现了响应等
            return False
        except Exception as e:
            log.debug(f"检查命令发送状态异常: {e}")
            return False

    def execute_voice_command_in_floating(self, command: str, duration: float = 3.0, language: str = "en") -> bool:
        """在浮窗中执行语音命令"""
        try:
            log.info(f"在浮窗中执行语音命令: {command}")

            if not self.is_floating_window_visible():
                log.error("浮窗不可见，无法执行语音命令")
                return False

            if self.floating_voice_button.is_exists():
                self.floating_voice_button.click()
                time.sleep(1)

                # 这里需要集成语音输入功能
                # 暂时使用文本输入作为替代
                log.info(f"语音命令转为文本输入: {command}")
                return self.execute_text_command_in_floating(command)
            else:
                log.error("❌ 未找到浮窗语音按钮")
                return False

        except Exception as e:
            log.error(f"在浮窗中执行语音命令异常: {e}")
            return False

    def _ensure_floating_input_ready(self) -> bool:
        """确保浮窗输入框就绪 - 基于实际DOM结构"""
        try:
            # 检查主输入框
            if self.floating_input_box.wait_for_element(timeout=3):
                if self.floating_input_box.is_enabled():
                    log.debug("主输入框已就绪")
                    return True

            # 检查输入框容器
            if self.floating_input_container.wait_for_element(timeout=2):
                log.debug("输入框容器已就绪")
                return True

            # 检查提示文本是否存在（说明输入区域已加载）
            if self.hint_text.is_exists():
                log.debug("通过提示文本检测到输入区域已就绪")
                return True

            # 检查备选输入框
            if self.floating_text_input.wait_for_element(timeout=2):
                if self.floating_text_input.is_enabled():
                    log.debug("备选输入框已就绪")
                    return True

            log.warning("浮窗输入框未就绪")
            return False

        except Exception as e:
            log.error(f"检查浮窗输入框就绪状态异常: {e}")
            return False

    # ==================== 响应处理方法 ====================

    def wait_for_floating_response(self, timeout: int = 10) -> bool:
        """等待浮窗AI响应（增强版本，多种检测方式）"""
        try:
            log.info(f"等待浮窗AI响应 (超时: {timeout}秒)")

            # 方法1: 等待专用响应区域出现内容
            try:
                if self.floating_response_area.wait_for_element(timeout=timeout//3):
                    log.info("✅ 浮窗响应区域已出现")
                    return True
            except Exception as e:
                log.debug(f"响应区域检测异常: {e}")

            # 方法2: 等待聊天列表更新
            try:
                if self.floating_chat_list.wait_for_element(timeout=timeout//3):
                    log.info("✅ 浮窗聊天列表已更新")
                    return True
            except Exception as e:
                log.debug(f"聊天列表检测异常: {e}")

            # 方法3: 等待任何新的文本内容出现
            try:
                if self.floating_response_text.wait_for_element(timeout=timeout//3):
                    log.info("✅ 浮窗响应文本已出现")
                    return True
            except Exception as e:
                log.debug(f"响应文本检测异常: {e}")

            # 方法4: 通过文本变化检测响应
            try:
                initial_text = self.get_floating_response_text()
                time.sleep(2)  # 等待2秒
                current_text = self.get_floating_response_text()

                if current_text != initial_text and len(current_text.strip()) > 0:
                    log.info("✅ 检测到浮窗文本内容变化")
                    return True
            except Exception as e:
                log.debug(f"文本变化检测异常: {e}")

            log.warning("❌ 浮窗响应等待超时")
            return False

        except Exception as e:
            log.error(f"等待浮窗响应异常: {e}")
            return False

    def get_floating_response_text(self) -> str:
        """获取浮窗响应文本 - 基于实际DOM结构"""
        try:
            # 方法1: 从问候语获取（如果是初始状态）
            if self.greeting_text.is_exists():
                text = self.greeting_text.get_text()
                if text and text.strip() and text.strip() != "Good morning!":
                    log.debug(f"从问候语获取到响应: {text.strip()}")
                    return text.strip()

            # 方法2: 从推荐项目文本获取
            if self.recommend_item_text.is_exists():
                text = self.recommend_item_text.get_text()
                if text and text.strip():
                    log.debug(f"从推荐项目获取到文本: {text.strip()}")
                    return text.strip()

            # 方法3: 从所有文本视图中查找AI响应
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for i in range(len(text_views) - 1, -1, -1):
                    try:
                        text = text_views[i].get_text()
                        if text and text.strip():
                            # 过滤掉系统文本和固定文本
                            filtered_texts = [
                                "Good morning!", "Feel free to ask me any questions…",
                                "DeepSeek-R1", "AI Wallpaper Generator", "Vogue Portraits"
                            ]
                            if text.strip() not in filtered_texts and len(text.strip()) > 10:
                                log.debug(f"从文本视图获取到响应: {text.strip()}")
                                return text.strip()
                    except Exception:
                        continue

            # 方法4: 从通用文本视图获取
            if self.floating_text_view.is_exists():
                text = self.floating_text_view.get_text()
                if text and text.strip():
                    log.debug(f"从通用文本视图获取到响应: {text.strip()}")
                    return text.strip()

            log.warning("未获取到浮窗响应文本")
            return ""

        except Exception as e:
            log.error(f"获取浮窗响应文本异常: {e}")
            return ""

    def get_all_floating_response_texts(self) -> list:
        """获取浮窗中所有响应文本 - 简化版本"""
        try:
            texts = []

            # 获取所有文本视图的内容
            text_views = self.driver(className="android.widget.TextView")
            if text_views.exists():
                for text_view in text_views:
                    text = text_view.get_text()
                    if text and text.strip():
                        texts.append(text.strip())

            return texts

        except Exception as e:
            log.error(f"获取所有浮窗响应文本异常: {e}")
            return []

    def get_response_all_text(self) -> list:
        """
        获取Ella浮窗返回的所有文案 - 增强版本，特别优化联系人和电话号码相关文案
        智能获取浮窗中的AI响应文本，支持多种获取方式

        Returns:
            list: 所有响应文本的列表
        """
        try:
            log.info("获取Ella浮窗返回的所有文案")

            # 确保浮窗可见
            if not self.is_floating_window_visible():
                log.warning("浮窗不可见，无法获取响应文案")
                return []

            all_texts = []

            # 方法1: 优先获取联系人和号码相关文案
            contact_texts = self._get_contact_related_texts()
            if contact_texts:
                all_texts.extend(contact_texts)
                log.info(f"获取到 {len(contact_texts)} 个联系人相关文案")

            # 方法2: 从问候语和提示文本获取
            greeting_text = self._get_greeting_text()
            if greeting_text:
                all_texts.append(greeting_text)

            # 方法3: 从推荐项目获取文本
            recommend_texts = self._get_recommend_texts()
            if recommend_texts:
                all_texts.extend(recommend_texts)

            # 方法4: 从所有TextView元素获取响应文本
            textview_texts = self._get_floating_textview_texts()
            if textview_texts:
                # 过滤掉系统文本和重复文本
                filtered_texts = self._filter_meaningful_texts(textview_texts)
                all_texts.extend(filtered_texts)

            # 方法5: 从页面dump中提取文本（备用方案）
            if not all_texts:
                dump_texts = self._get_texts_from_dump()
                if dump_texts:
                    all_texts.extend(dump_texts)

            # 去重并保持顺序
            unique_texts = []
            for text in all_texts:
                if text not in unique_texts:
                    unique_texts.append(text)

            # 特殊处理：确保联系人相关文案排在前面
            prioritized_texts = self._prioritize_contact_texts(unique_texts)

            log.info(f"✅ 获取到 {len(prioritized_texts)} 个响应文案")
            if prioritized_texts:
                log.debug(f"响应文案列表: {prioritized_texts}")

            return prioritized_texts

        except Exception as e:
            log.error(f"获取Ella浮窗响应文案失败: {e}")
            return []

    def _get_greeting_text(self) -> str:
        """获取问候语文本"""
        try:
            if self.greeting_text.is_exists():
                text = self.greeting_text.get_text()
                if text and text.strip() and text.strip() != "Good morning!":
                    log.debug(f"获取到问候语文本: {text.strip()}")
                    return text.strip()
            return ""
        except Exception as e:
            log.debug(f"获取问候语文本失败: {e}")
            return ""

    def _get_recommend_texts(self) -> list:
        """获取推荐项目文本"""
        try:
            texts = []

            # 从推荐项目文本获取
            if self.recommend_item_text.is_exists():
                text = self.recommend_item_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"获取到推荐项目文本: {text.strip()}")

            # 从推荐卡片列表获取
            if self.recommend_card_list.is_exists():
                try:
                    # 获取推荐卡片中的文本
                    card_texts = self.driver(resourceId="com.transsion.aivoiceassistant:id/tv_item_recommend")
                    if card_texts.exists():
                        for i in range(min(card_texts.count, 5)):  # 最多获取5个推荐
                            try:
                                text = card_texts[i].get_text()
                                if text and text.strip():
                                    texts.append(text.strip())
                                    log.debug(f"获取到推荐卡片文本[{i}]: {text.strip()}")
                            except Exception:
                                continue
                except Exception as e:
                    log.debug(f"获取推荐卡片文本失败: {e}")

            return texts

        except Exception as e:
            log.debug(f"获取推荐文本失败: {e}")
            return []

    def _get_contact_related_texts(self) -> list:
        """
        专门获取联系人和电话号码相关的文案

        Returns:
            list: 联系人相关文案列表
        """
        try:
            contact_texts = []

            # 方法1: 从专门的联系人元素获取
            contact_element_texts = self._get_contact_element_texts()
            if contact_element_texts:
                contact_texts.extend(contact_element_texts)

            # 方法2: 从通用文本中筛选联系人相关内容
            general_texts = self._get_floating_textview_texts()
            contact_filtered_texts = self._filter_contact_related_texts(general_texts)
            if contact_filtered_texts:
                contact_texts.extend(contact_filtered_texts)

            # 方法3: 从响应区域获取联系人相关文案
            response_contact_texts = self._get_response_contact_texts()
            if response_contact_texts:
                contact_texts.extend(response_contact_texts)

            # 去重
            unique_contact_texts = []
            for text in contact_texts:
                if text not in unique_contact_texts:
                    unique_contact_texts.append(text)

            log.debug(f"获取到 {len(unique_contact_texts)} 个联系人相关文案")
            return unique_contact_texts

        except Exception as e:
            log.error(f"获取联系人相关文案异常: {e}")
            return []

    def _get_contact_element_texts(self) -> list:
        """从专门的联系人元素获取文案"""
        try:
            texts = []

            # 从联系人名称文本获取
            if self.contact_name_text.is_exists():
                text = self.contact_name_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"从联系人名称元素获取: {text.strip()}")

            # 从手机号码文本获取
            if self.phone_number_text.is_exists():
                text = self.phone_number_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"从手机号码元素获取: {text.strip()}")

            # 从识别号码文本获取
            if self.recognized_number_text.is_exists():
                text = self.recognized_number_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"从识别号码元素获取: {text.strip()}")

            # 从联系人相关文本获取
            if self.contact_related_text.is_exists():
                text = self.contact_related_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"从联系人相关文本获取: {text.strip()}")

            # 从号码相关文本获取
            if self.number_related_text.is_exists():
                text = self.number_related_text.get_text()
                if text and text.strip():
                    texts.append(text.strip())
                    log.debug(f"从号码相关文本获取: {text.strip()}")

            return texts

        except Exception as e:
            log.debug(f"从联系人元素获取文案异常: {e}")
            return []

    def _filter_contact_related_texts(self, texts: list) -> list:
        """从通用文本中筛选联系人相关内容"""
        try:
            contact_keywords = [
                # 联系人相关关键词
                "contact", "联系人", "通讯录", "save", "保存", "add", "添加",
                # 号码相关关键词
                "number", "phone", "telephone", "号码", "电话", "手机",
                # 识别相关关键词
                "recognized", "识别", "detected", "检测", "found", "发现",
                # 确认相关关键词
                "confirm", "confirmation", "确认", "verify", "验证",
                # 操作相关关键词
                "following", "以下", "after", "之后", "can", "可以"
            ]

            contact_texts = []

            for text in texts:
                if not text or not text.strip():
                    continue

                text_lower = text.lower()

                # 检查是否包含联系人相关关键词
                contains_contact_keyword = any(keyword in text_lower for keyword in contact_keywords)

                # 检查是否包含数字（可能是电话号码）
                import re
                contains_numbers = bool(re.search(r'\d{3,}', text))

                # 如果包含联系人关键词或包含较长数字串，认为是联系人相关
                if contains_contact_keyword or contains_numbers:
                    contact_texts.append(text.strip())
                    log.debug(f"筛选出联系人相关文案: {text.strip()}")

            return contact_texts

        except Exception as e:
            log.debug(f"筛选联系人相关文案异常: {e}")
            return []

    def _get_response_contact_texts(self) -> list:
        """从响应区域获取联系人相关文案"""
        try:
            texts = []

            # 从浮窗响应区域获取
            if self.floating_response_area.is_exists():
                text = self.floating_response_area.get_text()
                if text and text.strip():
                    # 检查是否是联系人相关响应
                    if self._is_contact_related_text(text):
                        texts.append(text.strip())
                        log.debug(f"从响应区域获取联系人文案: {text.strip()}")

            # 从聊天列表获取最新的联系人相关消息
            if self.floating_chat_list.is_exists():
                try:
                    # 获取聊天列表中的文本元素
                    chat_texts = self.driver(resourceId="com.transsion.aivoiceassistant:id/rv_chat").child(className="android.widget.TextView")
                    if chat_texts.exists():
                        # 获取最近的几条消息
                        count = min(chat_texts.count, 5)
                        for i in range(count):
                            try:
                                text = chat_texts[i].get_text()
                                if text and text.strip() and self._is_contact_related_text(text):
                                    texts.append(text.strip())
                                    log.debug(f"从聊天列表获取联系人文案: {text.strip()}")
                            except Exception:
                                continue
                except Exception as e:
                    log.debug(f"从聊天列表获取文案失败: {e}")

            return texts

        except Exception as e:
            log.debug(f"从响应区域获取联系人文案异常: {e}")
            return []

    def _is_contact_related_text(self, text: str) -> bool:
        """判断文本是否与联系人相关"""
        try:
            if not text or len(text.strip()) < 5:
                return False

            text_lower = text.lower()

            # 联系人相关的关键短语
            contact_phrases = [
                "following number", "recognized", "save it as a contact",
                "contact after confirmation", "phone number", "add to contacts",
                "联系人", "号码", "识别", "保存", "确认", "添加", "通讯录"
            ]

            # 检查是否包含联系人相关短语
            for phrase in contact_phrases:
                if phrase in text_lower:
                    return True

            # 检查是否包含电话号码格式
            import re
            phone_patterns = [
                r'\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}',  # 常见电话号码格式
                r'\+\d{1,3}[-\s]?\d{3,4}[-\s]?\d{3,4}[-\s]?\d{4}',  # 国际号码格式
                r'\d{11}',  # 11位手机号
                r'\d{7,10}'  # 7-10位号码
            ]

            for pattern in phone_patterns:
                if re.search(pattern, text):
                    return True

            return False

        except Exception as e:
            log.debug(f"判断联系人相关文本异常: {e}")
            return False

    def _prioritize_contact_texts(self, texts: list) -> list:
        """将联系人相关文案排在前面"""
        try:
            contact_texts = []
            other_texts = []

            for text in texts:
                if self._is_contact_related_text(text):
                    contact_texts.append(text)
                else:
                    other_texts.append(text)

            # 联系人相关文案排在前面
            prioritized = contact_texts + other_texts

            if contact_texts:
                log.debug(f"优先排序: {len(contact_texts)} 个联系人文案, {len(other_texts)} 个其他文案")

            return prioritized

        except Exception as e:
            log.debug(f"优先排序文案异常: {e}")
            return texts

    def _get_floating_textview_texts(self, max_elements: int = 20) -> list:
        """获取浮窗中所有TextView元素的文本"""
        try:
            texts = []

            # 获取所有TextView元素
            text_views = self.driver(className="android.widget.TextView")
            if not text_views.exists():
                log.debug("未找到TextView元素")
                return []

            # 遍历TextView元素
            count = min(text_views.count, max_elements)
            log.debug(f"找到 {text_views.count} 个TextView元素，处理前 {count} 个")

            for i in range(count):
                try:
                    text = text_views[i].get_text()
                    if text and text.strip():
                        cleaned_text = text.strip()
                        if cleaned_text not in texts:  # 避免重复
                            texts.append(cleaned_text)
                            log.debug(f"获取到TextView文本[{i}]: {cleaned_text}")
                except Exception as e:
                    log.debug(f"获取第{i}个TextView文本失败: {e}")
                    continue

            log.debug(f"成功获取 {len(texts)} 个TextView文本")
            return texts

        except Exception as e:
            log.debug(f"获取TextView文本失败: {e}")
            return []

    def _filter_meaningful_texts(self, texts: list) -> list:
        """过滤出有意义的文本，排除系统文本和无关内容，特别保留联系人相关文案"""
        try:
            # 系统文本和固定文本列表
            system_texts = [
                "Good morning!", "Good afternoon!", "Good evening!",
                "Feel free to ask me any questions…",
                "DeepSeek-R1", "AI Wallpaper Generator", "Vogue Portraits",
                "Hi, I'm Ella", "Hello", "How can I help you?",
                "Ella is thinking…", "Please wait...",
                "Voice input", "Text input", "Send", "Cancel"
            ]

            meaningful_texts = []

            for text in texts:
                # 跳过空文本
                if not text or not text.strip():
                    continue

                cleaned_text = text.strip()

                # 跳过系统文本
                if cleaned_text in system_texts:
                    continue

                # 特殊处理：联系人相关文案优先保留
                if self._is_contact_related_text(cleaned_text):
                    if cleaned_text not in meaningful_texts:
                        meaningful_texts.append(cleaned_text)
                        log.debug(f"保留联系人相关文本: {cleaned_text}")
                    continue

                # 跳过太短的文本（可能是按钮文字等），但保留可能的电话号码
                if len(cleaned_text) < 3:
                    # 检查是否是短的但有意义的号码
                    import re
                    if not re.search(r'\d{3,}', cleaned_text):
                        continue

                # 跳过纯数字或特殊字符，但保留可能的电话号码
                if cleaned_text.isdigit():
                    # 如果是较长的数字串，可能是电话号码，保留
                    if len(cleaned_text) >= 7:
                        if cleaned_text not in meaningful_texts:
                            meaningful_texts.append(cleaned_text)
                            log.debug(f"保留可能的电话号码: {cleaned_text}")
                    continue

                if cleaned_text in ['...', '•', '○', '●']:
                    continue

                # 跳过重复文本
                if cleaned_text not in meaningful_texts:
                    meaningful_texts.append(cleaned_text)
                    log.debug(f"保留有意义文本: {cleaned_text}")

            return meaningful_texts

        except Exception as e:
            log.debug(f"过滤文本失败: {e}")
            return texts  # 失败时返回原始文本

    def _get_texts_from_dump(self) -> list:
        """从页面dump中提取文本（备用方案）"""
        try:
            log.debug("尝试从页面dump中提取文本")

            # 获取页面dump
            dump = self.driver.dump_hierarchy()
            if not dump:
                log.debug("无法获取页面dump")
                return []

            # 使用正则表达式提取text属性
            import re
            text_pattern = r'text="([^"]*)"'
            matches = re.findall(text_pattern, dump)

            # 清理和过滤文本
            extracted_texts = []
            for text in matches:
                cleaned_text = text.strip()
                if cleaned_text and len(cleaned_text) > 2:
                    extracted_texts.append(cleaned_text)

            # 过滤有意义的文本
            meaningful_texts = self._filter_meaningful_texts(extracted_texts)

            log.debug(f"从dump中提取到 {len(meaningful_texts)} 个有意义文本")
            return meaningful_texts

        except Exception as e:
            log.debug(f"从dump提取文本失败: {e}")
            return []

    def _is_ai_response_text(self, text: str) -> bool:
        """判断文本是否可能是AI响应"""
        try:
            if not text or len(text.strip()) < 5:
                return False

            # AI响应的特征
            ai_indicators = [
                "I", "you", "can", "help", "would", "could", "should",
                "here", "there", "what", "how", "when", "where", "why",
                "please", "thank", "sorry", "sure", "yes", "no"
            ]

            text_lower = text.lower()

            # 检查是否包含AI响应的常见词汇
            word_count = 0
            for indicator in ai_indicators:
                if indicator in text_lower:
                    word_count += 1

            # 如果包含多个AI响应词汇，认为是AI响应
            return word_count >= 2

        except Exception:
            return False

    # ==================== 状态检查方法 ====================

    def check_floating_window_status(self) -> dict:
        """检查浮窗状态"""
        try:
            status = {
                'visible': self.is_floating_window_visible(),
                'input_ready': self._ensure_floating_input_ready(),
                'position': None,
                'size': None
            }

            # 获取浮窗位置和大小
            if self.floating_container.is_exists():
                bounds = self.floating_container.get_bounds()
                if bounds:
                    # bounds 是元组格式: (left, top, right, bottom)
                    if isinstance(bounds, tuple) and len(bounds) >= 4:
                        left, top, right, bottom = bounds[0], bounds[1], bounds[2], bounds[3]
                        status['position'] = {
                            'x': (left + right) // 2,
                            'y': (top + bottom) // 2
                        }
                        status['size'] = {
                            'width': right - left,
                            'height': bottom - top
                        }
                    elif isinstance(bounds, dict):
                        # 如果是字典格式
                        status['position'] = {
                            'x': (bounds['left'] + bounds['right']) // 2,
                            'y': (bounds['top'] + bounds['bottom']) // 2
                        }
                        status['size'] = {
                            'width': bounds['right'] - bounds['left'],
                            'height': bounds['bottom'] - bounds['top']
                        }

            return status

        except Exception as e:
            log.error(f"检查浮窗状态异常: {e}")
            return {'visible': False, 'input_ready': False, 'position': None, 'size': None}

    # ==================== 兼容性方法 ====================

    def ensure_floating_window_ready(self) -> bool:
        """确保浮窗就绪"""
        try:
            log.info("确保浮窗就绪...")

            # 如果浮窗不可见，尝试打开
            if not self.is_floating_window_visible():
                log.info("浮窗不可见，尝试打开")
                if not self.open_floating_window():
                    log.error("无法打开浮窗")
                    return False

            # 等待浮窗完全加载
            time.sleep(2)

            # 检查输入框是否就绪
            if not self._ensure_floating_input_ready():
                log.warning("浮窗输入框未就绪，但浮窗已可见")
                return True  # 宽松策略

            log.info("✅ 浮窗已就绪")
            return True

        except Exception as e:
            log.error(f"确保浮窗就绪异常: {e}")
            return False

    # ==================== Ask Screen 联系人相关操作方法 ====================

    def get_contact_name(self) -> str:
        """
        获取联系人名称

        Returns:
            str: 联系人名称，如果获取失败返回空字符串
        """
        try:
            # 优先从输入框获取
            if self.contact_name_input.is_exists():
                name = self.contact_name_input.get_text()
                if name:
                    log.info(f"从输入框获取到联系人名称: {name}")
                    return name

            # 从文本显示区域获取
            if self.contact_name_text.is_exists():
                name = self.contact_name_text.get_text()
                if name:
                    log.info(f"从文本区域获取到联系人名称: {name}")
                    return name

            # 从通用联系人相关文本获取
            if self.contact_related_text.is_exists():
                text = self.contact_related_text.get_text()
                if text and "contact" in text.lower():
                    log.info(f"从通用文本获取到联系人相关信息: {text}")
                    return text

            log.warning("未找到联系人名称")
            return ""

        except Exception as e:
            log.error(f"获取联系人名称异常: {e}")
            return ""

    def get_phone_number(self) -> str:
        """
        获取手机号码

        Returns:
            str: 手机号码，如果获取失败返回空字符串
        """
        try:
            # 优先从识别到的号码区域获取
            if self.recognized_number_text.is_exists():
                number = self.recognized_number_text.get_text()
                if number:
                    log.info(f"从识别区域获取到号码: {number}")
                    return number

            # 从输入框获取
            if self.phone_number_input.is_exists():
                number = self.phone_number_input.get_text()
                if number:
                    log.info(f"从输入框获取到号码: {number}")
                    return number

            # 从文本显示区域获取
            if self.phone_number_text.is_exists():
                number = self.phone_number_text.get_text()
                if number:
                    log.info(f"从文本区域获取到号码: {number}")
                    return number

            # 从通用号码相关文本获取
            if self.number_related_text.is_exists():
                text = self.number_related_text.get_text()
                if text:
                    # 使用正则表达式提取号码
                    import re
                    numbers = re.findall(r'\d{3,}', text)
                    if numbers:
                        number = max(numbers, key=len)  # 选择最长的数字串
                        log.info(f"从通用文本提取到号码: {number}")
                        return number

            log.warning("未找到手机号码")
            return ""

        except Exception as e:
            log.error(f"获取手机号码异常: {e}")
            return ""

    def set_contact_name(self, name: str) -> bool:
        """
        设置联系人名称

        Args:
            name: 要设置的联系人名称

        Returns:
            bool: 设置是否成功
        """
        try:
            log.info(f"设置联系人名称: {name}")

            if self.contact_name_input.is_exists():
                self.contact_name_input.clear_text()
                self.contact_name_input.send_text(name)
                log.info("✅ 联系人名称设置成功")
                return True
            else:
                log.warning("❌ 未找到联系人名称输入框")
                return False

        except Exception as e:
            log.error(f"设置联系人名称异常: {e}")
            return False

    def set_phone_number(self, number: str) -> bool:
        """
        设置手机号码

        Args:
            number: 要设置的手机号码

        Returns:
            bool: 设置是否成功
        """
        try:
            log.info(f"设置手机号码: {number}")

            if self.phone_number_input.is_exists():
                self.phone_number_input.clear_text()
                self.phone_number_input.send_text(number)
                log.info("✅ 手机号码设置成功")
                return True
            else:
                log.warning("❌ 未找到手机号码输入框")
                return False

        except Exception as e:
            log.error(f"设置手机号码异常: {e}")
            return False

    def save_contact(self) -> bool:
        """
        保存联系人

        Returns:
            bool: 保存是否成功
        """
        try:
            log.info("尝试保存联系人")

            # 优先尝试保存联系人按钮
            if self.save_contact_button.is_exists():
                self.save_contact_button.click()
                log.info("✅ 点击保存联系人按钮成功")
                time.sleep(1)
                return True

            # 尝试添加到联系人按钮
            if self.add_to_contacts_button.is_exists():
                self.add_to_contacts_button.click()
                log.info("✅ 点击添加到联系人按钮成功")
                time.sleep(1)
                return True

            log.warning("❌ 未找到保存联系人的按钮")
            return False

        except Exception as e:
            log.error(f"保存联系人异常: {e}")
            return False

    def confirm_number(self) -> bool:
        """
        确认号码

        Returns:
            bool: 确认是否成功
        """
        try:
            log.info("尝试确认号码")

            if self.confirm_number_button.is_exists():
                self.confirm_number_button.click()
                log.info("✅ 点击确认号码按钮成功")
                time.sleep(1)
                return True
            else:
                log.warning("❌ 未找到确认号码按钮")
                return False

        except Exception as e:
            log.error(f"确认号码异常: {e}")
            return False

    def cancel_number(self) -> bool:
        """
        取消号码操作

        Returns:
            bool: 取消是否成功
        """
        try:
            log.info("尝试取消号码操作")

            if self.cancel_number_button.is_exists():
                self.cancel_number_button.click()
                log.info("✅ 点击取消号码按钮成功")
                time.sleep(1)
                return True
            else:
                log.warning("❌ 未找到取消号码按钮")
                return False

        except Exception as e:
            log.error(f"取消号码操作异常: {e}")
            return False

    def is_contact_info_visible(self) -> bool:
        """
        检查联系人信息是否可见

        Returns:
            bool: 联系人信息是否可见
        """
        try:
            # 检查联系人信息容器
            if self.contact_info_container.is_exists():
                return True

            # 检查联系人卡片容器
            if self.contact_card_container.is_exists():
                return True

            # 检查是否有联系人相关的输入框或文本
            if (self.contact_name_input.is_exists() or
                self.phone_number_input.is_exists() or
                self.contact_name_text.is_exists() or
                self.phone_number_text.is_exists()):
                return True

            return False

        except Exception as e:
            log.error(f"检查联系人信息可见性异常: {e}")
            return False

    def get_contact_info(self) -> dict:
        """
        获取完整的联系人信息

        Returns:
            dict: 包含联系人名称和手机号码的字典
        """
        try:
            contact_info = {
                'name': self.get_contact_name(),
                'phone': self.get_phone_number(),
                'visible': self.is_contact_info_visible()
            }

            log.info(f"获取到联系人信息: {contact_info}")
            return contact_info

        except Exception as e:
            log.error(f"获取联系人信息异常: {e}")
            return {'name': '', 'phone': '', 'visible': False}


if __name__ == '__main__':
    floating_page = EllaFloatingPage()
    # floating_page.trigger_ella_by_power_key(duration=3.0)
    # floating_page.wake_and_unlock_screen()
    # floating_page.open_floating_window()
    # floating_page.execute_text_command_in_floating(command="add this number")
    # floating_page.execute_text_command(command="add this number")
    # status = floating_page.check_floating_window_status()
    # print(f"浮窗状态: {status}")
    result = floating_page.is_floating_window_visible()
    print(f"浮窗可见: {result}")
    floating_page.close_floating_window()
    print(f"浮窗可见: {result}")
